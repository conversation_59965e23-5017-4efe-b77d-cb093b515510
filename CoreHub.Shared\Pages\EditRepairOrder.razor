@page "/edit-repair-order/{repairOrderId:int}"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Models.Dto
@using CoreHub.Shared.Services
@using CoreHub.Shared.Components
@using FluentValidation
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.Extensions.Logging
@using System.Security.Claims
@inject IRepairOrderService RepairOrderService
@inject IEquipmentService EquipmentService
@inject IEquipmentModelService EquipmentModelService
@inject IDepartmentService DepartmentService
@inject IUserManagementService UserManagementService
@inject IRoleDepartmentAssignmentServiceV2 RoleDepartmentAssignmentService
@inject IMaintenanceDepartmentPermissionService MaintenanceDepartmentPermissionService
@inject IDepartmentTypeService DepartmentTypeService
@inject IJobTypeService JobTypeService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthStateProvider
@inject ILogger<EditRepairOrder> Logger

<PageTitle>编辑报修单</PageTitle>

<AuthorizeView>
    <Authorized>
        <div>
            <MudPaper Class="pa-6">
                <MudGrid>
                    <MudItem xs="12">
                        <MudText Typo="Typo.h4" Class="mb-6">
                            <MudIcon Icon="@Icons.Material.Filled.Edit" Class="mr-2" />
                            编辑报修单
                            @if (!string.IsNullOrEmpty(repairOrder?.OrderNumber))
                            {
                                <MudChip T="string" Color="Color.Info" Size="Size.Small" Class="ml-2">
                                    @repairOrder.OrderNumber
                                </MudChip>
                            }
                        </MudText>
                    </MudItem>

                    @if (loading)
                    {
                        <MudItem xs="12" Class="text-center">
                            <MudProgressCircular Indeterminate="true" />
                            <MudText Class="mt-2">加载中...</MudText>
                        </MudItem>
                    }
                    else if (repairOrder == null)
                    {
                        <MudItem xs="12">
                            <MudAlert Severity="Severity.Error">
                                报修单不存在或已被删除
                            </MudAlert>
                        </MudItem>
                    }
                    else if (!canEdit)
                    {
                        <MudItem xs="12">
                            <MudAlert Severity="Severity.Warning">
                                @editErrorMessage
                            </MudAlert>
                        </MudItem>
                    }
                    else
                    {
                        <MudItem xs="12">
                            <MudForm @ref="form" Model="@repairOrder" Validation="@(new RepairOrderValidator())">
                                <MudGrid>
                                    <!-- 设备信息（只读显示） -->
                                    <MudItem xs="12">
                                        <MudCard>
                                            <MudCardHeader>
                                                <CardHeaderContent>
                                                    <MudText Typo="Typo.h6">
                                                        <MudIcon Icon="@Icons.Material.Filled.Devices" Class="mr-2" />
                                                        设备信息
                                                    </MudText>
                                                </CardHeaderContent>
                                            </MudCardHeader>
                                            <MudCardContent>
                                                @if (selectedEquipment != null)
                                                {
                                                    <MudAlert Severity="Severity.Info">
                                                        <MudText><strong>设备信息：</strong></MudText>
                                                        <MudText>名称：@selectedEquipment.Name</MudText>
                                                        <MudText>编码：@selectedEquipment.Code</MudText>
                                                        <MudText>位置：@selectedEquipment.DepartmentName - @selectedEquipment.LocationName</MudText>
                                                        <MudText>状态：@selectedEquipment.StatusName</MudText>
                                                    </MudAlert>
                                                }
                                            </MudCardContent>
                                        </MudCard>
                                    </MudItem>

                                    <!-- 故障信息 -->
                                    <MudItem xs="12">
                                        <MudCard>
                                            <MudCardHeader>
                                                <CardHeaderContent>
                                                    <MudText Typo="Typo.h6">
                                                        <MudIcon Icon="@Icons.Material.Filled.ReportProblem" Class="mr-2" />
                                                        故障信息
                                                    </MudText>
                                                </CardHeaderContent>
                                            </MudCardHeader>
                                            <MudCardContent>
                                                <MudGrid>
                                                    <MudItem xs="12" md="6">
                                                        <MudSelect T="int" @bind-Value="repairOrder.UrgencyLevel"
                                                            For="@(() => repairOrder.UrgencyLevel)" Label="紧急程度"
                                                            Required="true">
                                                            <MudSelectItem T="int" Value="1">
                                                                <div class="d-flex align-center">
                                                                    <MudIcon Icon="@Icons.Material.Filled.PriorityHigh"
                                                                        Color="Color.Error" Class="mr-2" />
                                                                    <span>紧急 - 设备完全停机，严重影响生产</span>
                                                                </div>
                                                            </MudSelectItem>
                                                            <MudSelectItem T="int" Value="2">
                                                                <div class="d-flex align-center">
                                                                    <MudIcon Icon="@Icons.Material.Filled.Warning"
                                                                        Color="Color.Warning" Class="mr-2" />
                                                                    <span>高 - 设备功能受限，影响正常使用</span>
                                                                </div>
                                                            </MudSelectItem>
                                                            <MudSelectItem T="int" Value="3">
                                                                <div class="d-flex align-center">
                                                                    <MudIcon Icon="@Icons.Material.Filled.Info"
                                                                        Color="Color.Info" Class="mr-2" />
                                                                    <span>中 - 设备有异常，但不影响基本功能</span>
                                                                </div>
                                                            </MudSelectItem>
                                                            <MudSelectItem T="int" Value="4">
                                                                <div class="d-flex align-center">
                                                                    <MudIcon Icon="@Icons.Material.Filled.Schedule"
                                                                        Color="Color.Default" Class="mr-2" />
                                                                    <span>低 - 预防性维护或小问题</span>
                                                                </div>
                                                            </MudSelectItem>
                                                        </MudSelect>
                                                    </MudItem>
                                                    <MudItem xs="12" md="6">
                                                        <MudSelect T="int" Value="repairOrder.MaintenanceDepartmentId"
                                                            For="@(() => repairOrder.MaintenanceDepartmentId)" Label="维修部门"
                                                            Required="true" ValueChanged="OnMaintenanceDepartmentChanged"
                                                            ToStringFunc="@(id => GetMaintenanceDepartmentDisplayName(id))">
                                                            @foreach (var dept in maintenanceDepartments)
                                                            {
                                                                <MudSelectItem T="int" Value="@dept.Id">
                                                                    <div class="d-flex align-center">
                                                                        <MudIcon Icon="@Icons.Material.Filled.Engineering" Size="Size.Small" Class="mr-2" />
                                                                        <span>@dept.Name</span>
                                                                        @if (dept.DepartmentType != null)
                                                                        {
                                                                            <MudChip T="string" Color="Color.Info" Size="Size.Small" Class="ml-2">
                                                                                @dept.DepartmentType.Name
                                                                            </MudChip>
                                                                        }
                                                                    </div>
                                                                </MudSelectItem>
                                                            }
                                                        </MudSelect>
                                                    </MudItem>
                                                    <MudItem xs="12" md="6">
                                                        <MudSelect T="int?" @bind-Value="selectedMaintenancePersonnelId"
                                                            Label="指定维修人员" Clearable="true"
                                                            Disabled="@(repairOrder.MaintenanceDepartmentId == 0 || !availablePersonnel.Any())">
                                                            @foreach (var personnel in availablePersonnel)
                                                            {
                                                                <MudSelectItem T="int?" Value="@(personnel.Id)">
                                                                    <div class="d-flex align-center">
                                                                        <div class="flex-grow-1">
                                                                            <MudText Typo="Typo.body2">@personnel.DisplayName</MudText>
                                                                            <MudStack Row Spacing="1" Class="mt-1">
                                                                                <MudChip T="string" Color="Color.Primary" Size="Size.Small">
                                                                                    维修工种
                                                                                </MudChip>
                                                                                <MudChip T="string" Color="Color.Success" Size="Size.Small">
                                                                                    可接单
                                                                                </MudChip>
                                                                            </MudStack>
                                                                            <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                                                部门: @personnel.Department?.Name
                                                                            </MudText>
                                                                        </div>
                                                                    </div>
                                                                </MudSelectItem>
                                                            }
                                                        </MudSelect>
                                                        @if (repairOrder.MaintenanceDepartmentId > 0 && !availablePersonnel.Any())
                                                        {
                                                            <MudText Typo="Typo.caption" Color="Color.Warning" Class="mt-1">
                                                                该部门暂无可用维修人员，系统将自动分配
                                                            </MudText>
                                                        }
                                                    </MudItem>
                                                    <MudItem xs="12">
                                                        <MudTextField @bind-Value="repairOrder.FaultDescription"
                                                            For="@(() => repairOrder.FaultDescription)" Label="故障描述"
                                                            Required="true" Lines="5"
                                                            Placeholder="请详细描述设备故障现象、发生时间、可能原因等..."
                                                            HelperText="详细的故障描述有助于维修人员快速定位问题" Immediate="true" />
                                                    </MudItem>
                                                    <MudItem xs="12">
                                                        <MudTextField @bind-Value="repairOrder.Remark"
                                                            For="@(() => repairOrder.Remark)" Label="补充说明" Lines="2"
                                                            Placeholder="其他需要说明的情况..." Immediate="true" />
                                                    </MudItem>
                                                </MudGrid>
                                            </MudCardContent>
                                        </MudCard>
                                    </MudItem>

                                    <!-- 操作按钮 -->
                                    <MudItem xs="12">
                                        <MudStack Row Justify="Justify.Center" Spacing="4" Class="mt-6">
                                            <MudButton Variant="Variant.Outlined" StartIcon="@Icons.Material.Filled.Cancel"
                                                OnClick="Cancel">
                                                取消
                                            </MudButton>
                                            <MudButton Variant="Variant.Filled" Color="Color.Primary"
                                                StartIcon="@Icons.Material.Filled.Save" OnClick="UpdateRepairOrder"
                                                Disabled="@submitting">
                                                @if (submitting)
                                                {
                                                    <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                                                    <MudText Class="ms-2">保存中...</MudText>
                                                }
                                                else
                                                {
                                                    <MudText>保存修改</MudText>
                                                }
                                            </MudButton>
                                        </MudStack>
                                    </MudItem>
                                </MudGrid>
                            </MudForm>
                        </MudItem>
                    }
                </MudGrid>
            </MudPaper>
        </div>
    </Authorized>
    <NotAuthorized>
        <div>
            <MudPaper Class="pa-6">
                <MudStack AlignItems="AlignItems.Center" Spacing="4">
                    <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Large" Color="Color.Warning" />
                    <MudText Typo="Typo.h5">需要登录</MudText>
                    <MudText Typo="Typo.body1" Color="Color.Secondary">请先登录后再访问此页面</MudText>
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="/login"
                        StartIcon="@Icons.Material.Filled.Login">
                        前往登录
                    </MudButton>
                </MudStack>
            </MudPaper>
        </div>
    </NotAuthorized>
</AuthorizeView>

@code {
    [Parameter] public int RepairOrderId { get; set; }

    private MudForm form = null!;
    private RepairOrder? repairOrder = null;
    private List<Department> maintenanceDepartments = new();
    private List<User> availablePersonnel = new();
    private EquipmentDetailDto? selectedEquipment = null;
    private int? selectedMaintenancePersonnelId = null;
    private bool submitting = false;
    private bool loading = true;
    private bool canEdit = false;
    private string editErrorMessage = string.Empty;
    private int currentUserId = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentUser();
        await LoadRepairOrder();
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var userIdClaim = authState.User.FindFirst("UserId") ?? authState.User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
                {
                    currentUserId = userId;
                }
                else
                {
                    currentUserId = 1;
                    Snackbar.Add("无法获取用户ID，使用默认值", Severity.Warning);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"获取当前用户信息失败: {ex.Message}", Severity.Error);
            currentUserId = 1;
        }
    }

    private async Task LoadRepairOrder()
    {
        try
        {
            loading = true;

            // 获取报修单信息
            repairOrder = await RepairOrderService.GetRepairOrderByIdAsync(RepairOrderId);

            if (repairOrder == null)
            {
                editErrorMessage = "报修单不存在";
                canEdit = false;
                return;
            }

            // 检查编辑权限
            if (repairOrder.ReporterId != currentUserId)
            {
                editErrorMessage = "您只能编辑自己创建的报修单";
                canEdit = false;
                return;
            }

            if (repairOrder.Status != CoreHub.Shared.Utils.RepairOrderStatusHelper.Pending)
            {
                editErrorMessage = "只有待处理状态的报修单才能编辑";
                canEdit = false;
                return;
            }

            canEdit = true;

            // 加载设备信息
            await LoadEquipmentInfo();

            // 加载维修部门信息
            await LoadMaintenanceDepartments();

            // 设置当前选择的维修人员
            selectedMaintenancePersonnelId = repairOrder.AssignedTo > 0 ? repairOrder.AssignedTo : null;

            // 加载维修人员
            if (repairOrder.MaintenanceDepartmentId > 0)
            {
                await LoadMaintenancePersonnel();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载报修单失败: {RepairOrderId}", RepairOrderId);
            Snackbar.Add($"加载报修单失败: {ex.Message}", Severity.Error);
            editErrorMessage = "加载报修单失败";
            canEdit = false;
        }
        finally
        {
            loading = false;
            StateHasChanged();
        }
    }

    private async Task LoadEquipmentInfo()
    {
        try
        {
            if (repairOrder?.EquipmentId > 0)
            {
                var equipmentList = await EquipmentService.GetEquipmentDetailsAsync();
                selectedEquipment = equipmentList.FirstOrDefault(e => e.Id == repairOrder.EquipmentId);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载设备信息失败: {EquipmentId}", repairOrder?.EquipmentId);
            Snackbar.Add($"加载设备信息失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadMaintenanceDepartments()
    {
        try
        {
            if (selectedEquipment != null)
            {
                // 根据设备所属部门获取可以维修该部门设备的维修部门
                var availableMaintenanceDepts = await MaintenanceDepartmentPermissionService.GetAvailableMaintenanceDepartmentsAsync(selectedEquipment.DepartmentId);

                // 如果没有配置权限关系，则显示所有维修部门
                if (!availableMaintenanceDepts.Any())
                {
                    availableMaintenanceDepts = await DepartmentTypeService.GetMaintenanceDepartmentsAsync();
                }

                // 加载部门类型信息
                foreach (var dept in availableMaintenanceDepts)
                {
                    if (dept.DepartmentTypeId.HasValue)
                    {
                        dept.DepartmentType = await DepartmentTypeService.GetDepartmentTypeByIdAsync(dept.DepartmentTypeId.Value);
                    }
                }

                maintenanceDepartments = availableMaintenanceDepts;
            }
            else
            {
                // 如果没有设备信息，加载所有维修部门
                maintenanceDepartments = await DepartmentTypeService.GetMaintenanceDepartmentsAsync();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载维修部门失败");
            Snackbar.Add($"加载维修部门失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadMaintenancePersonnel()
    {
        try
        {
            if (repairOrder?.MaintenanceDepartmentId > 0)
            {
                availablePersonnel = await JobTypeService.GetMaintenanceUsersByDepartmentAsync(repairOrder.MaintenanceDepartmentId);
            }
            else
            {
                availablePersonnel.Clear();
            }
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载维修人员失败");
            Snackbar.Add($"加载维修人员失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task OnMaintenanceDepartmentChanged(int departmentId)
    {
        if (repairOrder != null)
        {
            repairOrder.MaintenanceDepartmentId = departmentId;
            selectedMaintenancePersonnelId = null;
            await LoadMaintenancePersonnel();
        }
    }

    // 获取维修部门显示名称
    private string GetMaintenanceDepartmentDisplayName(int departmentId)
    {
        if (departmentId == 0) return string.Empty;

        var department = maintenanceDepartments.FirstOrDefault(d => d.Id == departmentId);
        if (department != null)
        {
            return department.Name;
        }

        return departmentId.ToString();
    }

    private void Cancel()
    {
        Navigation.NavigateTo("/repair-order-management");
    }

    private async Task UpdateRepairOrder()
    {
        if (repairOrder == null || !canEdit)
        {
            Snackbar.Add("无法保存修改", Severity.Error);
            return;
        }

        await form.Validate();
        if (!form.IsValid) return;

        submitting = true;
        try
        {
            // 如果用户指定了维修人员，设置到报修单
            if (selectedMaintenancePersonnelId.HasValue)
            {
                repairOrder.AssignedTo = selectedMaintenancePersonnelId.Value;
            }
            else if (availablePersonnel.Any())
            {
                // 简单分配：选择第一个可用的维修人员
                var firstAvailablePersonnel = availablePersonnel.First();
                repairOrder.AssignedTo = firstAvailablePersonnel.Id;
                Snackbar.Add($"已自动分配维修人员：{firstAvailablePersonnel.DisplayName}", Severity.Info);
            }

            var result = await RepairOrderService.UpdateRepairOrderAsync(repairOrder);
            if (result.IsSuccess)
            {
                Snackbar.Add("报修单修改成功！", Severity.Success);
                Navigation.NavigateTo("/repair-order-management");
            }
            else
            {
                Snackbar.Add($"修改失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "更新报修单失败: {RepairOrderId}", RepairOrderId);
            Snackbar.Add($"修改失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            submitting = false;
        }
    }

    public class RepairOrderValidator : AbstractValidator<RepairOrder>
    {
        public RepairOrderValidator()
        {
            RuleFor(x => x.EquipmentId)
                .GreaterThan(0).WithMessage("请选择设备");

            RuleFor(x => x.FaultDescription)
                .NotEmpty().WithMessage("请描述故障现象")
                .MinimumLength(10).WithMessage("故障描述至少需要10个字符")
                .MaximumLength(1000).WithMessage("故障描述不能超过1000个字符");

            RuleFor(x => x.UrgencyLevel)
                .InclusiveBetween(1, 4).WithMessage("请选择紧急程度");

            RuleFor(x => x.MaintenanceDepartmentId)
                .GreaterThan(0).WithMessage("请选择维修部门");

            RuleFor(x => x.Remark)
                .MaximumLength(1000).WithMessage("补充说明不能超过1000个字符");
        }

        public Func<object, string, Task<IEnumerable<string>>> ValidateValue => async (model, propertyName) =>
        {
            var result = await ValidateAsync(ValidationContext<RepairOrder>.CreateWithOptions((RepairOrder)model, x =>
                x.IncludeProperties(propertyName)));
            if (result.IsValid)
                return Array.Empty<string>();
            return result.Errors.Select(e => e.ErrorMessage);
        };
    }
}
