using CoreHub.Web.Components;
using CoreHub.Web.Services;
using CoreHub.Shared.Services;
using CoreHub.Shared.Data;
using Microsoft.AspNetCore.Components.Authorization;
using CoreHub.Shared.Configuration;
using MudBlazor.Services;
using Serilog;

namespace CoreHub.Web
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            // 配置早期日志记录
            Log.Logger = new LoggerConfiguration()
                .WriteTo.Console()
                .CreateBootstrapLogger();

            try
            {
                Log.Information("正在启动 CoreHub Web 应用程序");

                // 显示环境配置信息
                Log.Information("=== CoreHub 环境配置信息 ===");
                Log.Information("COREHUB_ENVIRONMENT: {Environment}", Environment.GetEnvironmentVariable("COREHUB_ENVIRONMENT") ?? "未设置");
                Log.Information("COREHUB_API_BASE_URL: {ApiUrl}", Environment.GetEnvironmentVariable("COREHUB_API_BASE_URL") ?? "未设置");
                Log.Information("当前环境: {CurrentEnv}", EnvironmentConfig.CurrentEnvironment);
                Log.Information("是否为生产环境: {IsProduction}", EnvironmentConfig.IsProduction);
                Log.Information("API基础URL: {ApiBaseUrl}", EnvironmentConfig.GetApiBaseUrl());
                Log.Information("================================");

                var builder = WebApplication.CreateBuilder(args);

                // 配置 Serilog
                builder.Host.UseSerilog((context, configuration) =>
                {
                    configuration
                        .ReadFrom.Configuration(context.Configuration)
                        .WriteTo.Console()
                        .WriteTo.File("logs/corehub-web-.log", rollingInterval: RollingInterval.Day)
                        .Enrich.FromLogContext()
                        .Enrich.WithProperty("Application", "CoreHub.Web");
                });

                // Add services to the container.
                builder.Services.AddRazorComponents()
                    .AddInteractiveServerComponents();
                builder.Services.AddMudServices();

                // 添加控制器支持（用于API端点）
                builder.Services.AddControllers();

                // 添加HttpClient服务（用于文件上传等功能）
                builder.Services.AddHttpClient();

                // 配置文件上传大小限制
                builder.Services.Configure<IISServerOptions>(options =>
                {
                    options.MaxRequestBodySize = 104857600; // 100MB
                });

                // Add CORS services
                builder.Services.AddCors(options =>
                {
                    options.AddDefaultPolicy(
                        builder =>
                        {
                            builder.AllowAnyOrigin()
                                   .AllowAnyMethod()
                                   .AllowAnyHeader();
                        });
                });

                // 添加Authorization服务
                builder.Services.AddAuthorizationCore();

                // 注册日志服务
                builder.Services.AddSingleton<IApplicationLogger, ApplicationLogger>();

                // 注册SSL证书管理服务
                builder.Services.AddSingleton<ISslCertificateService, SslCertificateService>();
                builder.Services.AddScoped<ICertificateReplacementService, CertificateReplacementService>();
                builder.Services.AddSingleton<ICertificateMonitoringService, CertificateMonitoringService>();

                // 配置证书监控选项
                builder.Services.Configure<CertificateMonitoringOptions>(
                    builder.Configuration.GetSection(CertificateMonitoringOptions.SectionName));

                // 注册FormFactor服务
                builder.Services.AddScoped<IFormFactor, FormFactor>();

                // 注册二维码扫描服务
                builder.Services.AddScoped<IQrCodeScannerService, WebQrCodeScannerService>();

                // 注册通知服务
                builder.Services.AddScoped<INotificationService, WebNotificationService>();

                // 数据库配置现在直接从 appsettings.json 读取，支持环境变量覆盖

                // 注册数据库上下文
                builder.Services.AddScoped<DatabaseContext>();

                // 根据配置文件选择认证服务实现
                var useStoredProcedure = builder.Configuration.GetValue<bool>("AuthenticationSettings:UseStoredProcedure");
                if (useStoredProcedure)
                {
                    builder.Services.AddScoped<IUserAuthenticationService, StoredProcedureAuthenticationService>();
                }
                else
                {
                    builder.Services.AddScoped<IUserAuthenticationService, DatabaseAuthenticationService>();
                }

                // 注册Web平台的认证状态存储
                builder.Services.AddScoped<IAuthenticationStateStorage, WebAuthenticationStateStorage>();

                // 注册持久化认证状态提供器
                builder.Services.AddScoped<AuthenticationStateProvider, PersistentAuthenticationStateProvider>();

                // 注册用户管理服务
                builder.Services.AddScoped<IUserManagementService, UserManagementService>();

                // 注册菜单服务
                builder.Services.AddScoped<IMenuService, MenuService>();

                // 注册设备管理服务
                builder.Services.AddScoped<IDepartmentService, DepartmentService>();
                builder.Services.AddScoped<IEquipmentModelService, EquipmentModelService>();
                builder.Services.AddScoped<ILocationService, LocationService>();
                builder.Services.AddScoped<IEquipmentService, EquipmentService>();
                builder.Services.AddScoped<IRepairOrderPartRequestService, RepairOrderPartRequestService>();
                builder.Services.AddScoped<IRepairOrderService, RepairOrderService>();

                // 注册权限服务
                builder.Services.AddScoped<IRoleDepartmentPermissionService, RoleDepartmentPermissionService>();
                builder.Services.AddScoped<IRoleDepartmentAssignmentServiceV2, RoleDepartmentAssignmentServiceV2>();
                builder.Services.AddScoped<IMaintenanceDepartmentPermissionService, MaintenanceDepartmentPermissionService>();
                builder.Services.AddScoped<IPermissionValidationService, PermissionValidationService>();

                // 注册部门类型和工种类型服务
                builder.Services.AddScoped<IDepartmentTypeService, DepartmentTypeService>();
                builder.Services.AddScoped<IJobTypeService, JobTypeService>();

                // 注册维修工作流服务
                builder.Services.AddScoped<IMaintenanceDashboardService, MaintenanceDashboardService>();
                builder.Services.AddScoped<IRepairWorkflowService, RepairWorkflowService>();

                // 注册委外加工服务
                builder.Services.AddScoped<OutsourcedProcessingService>();

                // 注册应用更新服务
                builder.Services.AddScoped<IAppUpdateService, AppUpdateService>();

                // 注册Web平台的更新UI服务
                builder.Services.AddScoped<IUpdateUIService, WebUpdateUIService>();

                // 注册Web平台的客户端更新服务（空实现）
                builder.Services.AddScoped<IClientUpdateService, WebClientUpdateService>();

                var app = builder.Build();

                // 验证SSL证书
                await ValidateSslCertificateAsync(app.Services, app.Configuration);

                // 启动证书监控服务
                await StartCertificateMonitoringAsync(app.Services);

                // 配置请求日志
                app.UseSerilogRequestLogging();

                Log.Information("正在配置 HTTP 请求管道");

                // Configure the HTTP request pipeline.
                if (!app.Environment.IsDevelopment())
                {
                    app.UseExceptionHandler("/Error", createScopeForErrors: true);
                    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                    app.UseHsts();
                }

                // 根据appsettings.json中的UseHttpsRedirection配置决定是否启用HTTPS重定向
                var useHttpsRedirection = app.Configuration.GetValue<bool>("UseHttpsRedirection", false);
                if (useHttpsRedirection)
                {
                    app.UseHttpsRedirection();
                }

                app.UseStaticFiles();
                app.UseAntiforgery();
                app.UseCors();

                // 映射控制器路由（API路由应该在Blazor路由之前）
                app.MapControllers();

                // 映射Blazor组件路由
                app.MapRazorComponents<App>()
                    .AddInteractiveServerRenderMode()
                    .AddAdditionalAssemblies(typeof(CoreHub.Shared._Imports).Assembly);

                // 添加Fallback路由，处理所有未匹配的路由
                app.MapFallback(context =>
                {
                    context.Response.Redirect("/404");
                    return Task.CompletedTask;
                });





                // 确保更新文件目录存在
                EnsureUpdateDirectoryExists();

                Log.Information("CoreHub Web 应用程序启动完成");
                app.Run();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "应用程序启动失败");
                throw;
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        /// <summary>
        /// 验证SSL证书
        /// </summary>
        /// <param name="services">服务提供者</param>
        /// <param name="configuration">配置</param>
        private static async Task ValidateSslCertificateAsync(IServiceProvider services, IConfiguration configuration)
        {
            try
            {
                var certPath = configuration["Kestrel:EndPoints:Https:Certificate:Path"];
                var certPassword = configuration["Kestrel:EndPoints:Https:Certificate:Password"];

                if (string.IsNullOrEmpty(certPath) || string.IsNullOrEmpty(certPassword))
                {
                    Log.Warning("SSL证书配置不完整，跳过证书验证");
                    return;
                }

                using var scope = services.CreateScope();
                var sslService = scope.ServiceProvider.GetRequiredService<ISslCertificateService>();

                var validationResult = await sslService.ValidateCertificateAsync(certPath, certPassword);

                if (!validationResult.IsValid)
                {
                    Log.Error("SSL证书验证失败: {ErrorMessage}", validationResult.ErrorMessage);
                    throw new InvalidOperationException($"SSL证书验证失败: {validationResult.ErrorMessage}");
                }

                if (validationResult.IsExpiringSoon)
                {
                    Log.Warning("SSL证书即将过期: {ExpiryDate}, 剩余天数: {DaysRemaining}",
                        validationResult.NotAfter, validationResult.DaysUntilExpiry);
                }
                else
                {
                    Log.Information("SSL证书验证成功，有效期至: {ExpiryDate}", validationResult.NotAfter);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "SSL证书验证过程中发生错误");
                // 在开发环境中不阻止启动，生产环境中可以考虑阻止启动
                // throw;
            }
        }

        /// <summary>
        /// 确保更新文件目录存在
        /// </summary>
        private static void EnsureUpdateDirectoryExists()
        {
            try
            {
                var updatesPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "updates");
                if (!Directory.Exists(updatesPath))
                {
                    Directory.CreateDirectory(updatesPath);
                    Log.Information("应用启动时自动创建更新文件目录: {UpdatesPath}", updatesPath);
                }
                else
                {
                    Log.Information("更新文件目录已存在: {UpdatesPath}", updatesPath);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "创建更新文件目录时发生异常");
            }
        }

        /// <summary>
        /// 启动证书监控服务
        /// </summary>
        /// <param name="services">服务提供者</param>
        private static async Task StartCertificateMonitoringAsync(IServiceProvider services)
        {
            try
            {
                var monitoringService = services.GetRequiredService<ICertificateMonitoringService>();
                await monitoringService.StartMonitoringAsync();
                Log.Information("SSL证书监控服务已启动");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "启动SSL证书监控服务失败");
            }
        }
    }
}
