@page "/user-department-assignment-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@using MudBlazor
@inject IUserManagementService UserManagementService
@inject IDepartmentService DepartmentService
@inject ISnackbar Snackbar
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>用户所属部门管理</PageTitle>

<AuthorizeView>
    <Authorized>
        <div>
            <MudPaper Class="pa-6">
                <MudGrid>
                    <!-- 页面标题 -->
                    <MudItem xs="12">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="3" Class="mb-6">
                            <MudIcon Icon="@Icons.Material.Filled.PersonPin" Size="Size.Large" Color="Color.Primary" />
                            <div>
                                <MudText Typo="Typo.h4">用户所属部门管理</MudText>
                                <MudText Typo="Typo.body1" Color="Color.Secondary">
                                    设置用户在组织架构中的所属部门，用于维修人员筛选和组织管理
                                </MudText>
                                <MudText Typo="Typo.caption" Color="Color.Info" Class="mt-1">
                                    💡 注意：用户所属部门与角色权限部门是不同的概念。所属部门表示用户的组织归属，权限部门表示可访问的数据范围。
                                </MudText>
                            </div>
                            <MudSpacer />
                            <MudButton Variant="Variant.Outlined" Color="Color.Primary" 
                                StartIcon="@Icons.Material.Filled.Refresh" OnClick="LoadData">
                                刷新数据
                            </MudButton>
                        </MudStack>
                    </MudItem>

                    <!-- 统计信息 -->
                    <MudItem xs="12">
                        <MudGrid>
                            <MudItem xs="6" sm="3">
                                <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Large" />
                                        <div>
                                            <MudText Typo="Typo.h4">@users.Count</MudText>
                                            <MudText Typo="Typo.body2">总用户数</MudText>
                                        </div>
                                    </MudStack>
                                </MudPaper>
                            </MudItem>
                            <MudItem xs="6" sm="3">
                                <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@Icons.Material.Filled.Business" Size="Size.Large" />
                                        <div>
                                            <MudText Typo="Typo.h4">@departments.Count</MudText>
                                            <MudText Typo="Typo.body2">总部门数</MudText>
                                        </div>
                                    </MudStack>
                                </MudPaper>
                            </MudItem>
                            <MudItem xs="6" sm="3">
                                <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Large" />
                                        <div>
                                            <MudText Typo="Typo.h4">@assignedUsersCount</MudText>
                                            <MudText Typo="Typo.body2">已分配用户</MudText>
                                        </div>
                                    </MudStack>
                                </MudPaper>
                            </MudItem>
                            <MudItem xs="6" sm="3">
                                <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white;">
                                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@Icons.Material.Filled.Warning" Size="Size.Large" />
                                        <div>
                                            <MudText Typo="Typo.h4">@unassignedUsersCount</MudText>
                                            <MudText Typo="Typo.body2">未分配用户</MudText>
                                        </div>
                                    </MudStack>
                                </MudPaper>
                            </MudItem>
                        </MudGrid>
                    </MudItem>

                    <!-- 用户部门分配 -->
                    <MudItem xs="12">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">用户部门分配</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <!-- 搜索和筛选 -->
                                    <MudItem xs="12" md="6">
                                        <MudTextField @bind-Value="searchKeyword" 
                                                    Label="搜索用户" 
                                                    Variant="Variant.Outlined"
                                                    Adornment="Adornment.Start" 
                                                    AdornmentIcon="@Icons.Material.Filled.Search"
                                                    OnKeyUp="OnSearchKeyUp" />
                                    </MudItem>
                                    <MudItem xs="12" md="6">
                                        <MudSelect T="int?" Value="selectedDepartmentFilter"
                                                 Label="按部门筛选"
                                                 Variant="Variant.Outlined"
                                                 Clearable="true"
                                                 ValueChanged="OnDepartmentFilterChanged">
                                            @foreach (var dept in departments)
                                            {
                                                <MudSelectItem T="int?" Value="@dept.Id">@dept.Name</MudSelectItem>
                                            }
                                        </MudSelect>
                                    </MudItem>
                                </MudGrid>

                                <!-- 用户列表 -->
                                <MudDataGrid T="User" Items="@filteredUsers" 
                                           Filterable="false" 
                                           SortMode="SortMode.Multiple" 
                                           Hover="true" 
                                           Striped="true"
                                           Dense="true"
                                           Class="mt-4">
                                    <Columns>
                                        <PropertyColumn Property="x => x.Username" Title="用户名" />
                                        <PropertyColumn Property="x => x.DisplayName" Title="显示名称" />
                                        <TemplateColumn Title="当前部门" Sortable="false">
                                            <CellTemplate Context="userContext">
                                                @if (userContext.Item.DepartmentId.HasValue)
                                                {
                                                    var department = departments.FirstOrDefault(d => d.Id == userContext.Item.DepartmentId.Value);
                                                    if (department != null)
                                                    {
                                                        <MudChip Color="Color.Primary" Size="Size.Small">@department.Name</MudChip>
                                                    }
                                                    else
                                                    {
                                                        <MudChip Color="Color.Warning" Size="Size.Small">部门不存在</MudChip>
                                                    }
                                                }
                                                else
                                                {
                                                    <MudChip Color="Color.Default" Size="Size.Small">未分配</MudChip>
                                                }
                                            </CellTemplate>
                                        </TemplateColumn>
                                        <TemplateColumn Title="分配部门" Sortable="false">
                                            <CellTemplate Context="userContext">
                                                <MudSelect T="int?" Value="@userContext.Item.DepartmentId"
                                                         ValueChanged="@(async (int? value) => await UpdateUserDepartment(userContext.Item, value))"
                                                         Label="选择部门"
                                                         Variant="Variant.Outlined"
                                                         Clearable="true"
                                                         Dense="true">
                                                    @foreach (var dept in departments)
                                                    {
                                                        <MudSelectItem T="int?" Value="@dept.Id">@dept.Name</MudSelectItem>
                                                    }
                                                </MudSelect>
                                            </CellTemplate>
                                        </TemplateColumn>
                                        <TemplateColumn Title="状态" Sortable="false">
                                            <CellTemplate Context="userContext">
                                                @if (userContext.Item.IsEnabled)
                                                {
                                                    <MudChip Color="Color.Success" Size="Size.Small">已启用</MudChip>
                                                }
                                                else
                                                {
                                                    <MudChip Color="Color.Error" Size="Size.Small">已禁用</MudChip>
                                                }
                                            </CellTemplate>
                                        </TemplateColumn>
                                    </Columns>
                                </MudDataGrid>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </div>
    </Authorized>
    <NotAuthorized>
        <MudAlert Severity="Severity.Warning">您没有权限访问此页面</MudAlert>
    </NotAuthorized>
</AuthorizeView>

@code {
    private List<User> users = new();
    private List<User> filteredUsers = new();
    private List<Department> departments = new();
    private string searchKeyword = "";
    private int? selectedDepartmentFilter = null;
    private bool isLoading = false;
    private int currentUserId = 0;

    // 统计信息
    private int assignedUsersCount => users.Count(u => u.DepartmentId.HasValue);
    private int unassignedUsersCount => users.Count(u => !u.DepartmentId.HasValue);

    protected override async Task OnInitializedAsync()
    {
        await GetCurrentUserId();
        await LoadData();
    }

    private async Task GetCurrentUserId()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            var userIdClaim = authState.User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim != null && int.TryParse(userIdClaim.Value, out var userId))
            {
                currentUserId = userId;
            }
        }
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            // 加载用户和部门数据
            var userResult = await UserManagementService.GetUsersAsync(1, 1000);
            users = userResult.Users;
            departments = await DepartmentService.GetEnabledDepartmentsAsync();

            FilterUsers();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载数据失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void FilterUsers()
    {
        var query = users.AsEnumerable();

        // 按关键词搜索
        if (!string.IsNullOrWhiteSpace(searchKeyword))
        {
            query = query.Where(u =>
                u.Username.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) ||
                u.DisplayName.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase));
        }

        // 按部门筛选
        if (selectedDepartmentFilter.HasValue)
        {
            query = query.Where(u => u.DepartmentId == selectedDepartmentFilter.Value);
        }

        filteredUsers = query.ToList();
        StateHasChanged();
    }

    private void OnSearchKeyUp(KeyboardEventArgs e)
    {
        FilterUsers();
    }

    private void OnDepartmentFilterChanged(int? departmentId)
    {
        selectedDepartmentFilter = departmentId;
        FilterUsers();
    }

    private async Task UpdateUserDepartment(User user, int? departmentId)
    {
        try
        {
            // 更新用户对象
            user.DepartmentId = departmentId;

            // 调用服务更新
            var result = await UserManagementService.UpdateUserAsync(user);
            if (result.IsSuccess)
            {
                var departmentName = departmentId.HasValue 
                    ? departments.FirstOrDefault(d => d.Id == departmentId.Value)?.Name ?? "未知部门"
                    : "无";
                
                Snackbar.Add($"用户 '{user.DisplayName}' 的部门已更新为: {departmentName}", Severity.Success);
                
                // 重新加载数据以确保同步
                await LoadData();
            }
            else
            {
                Snackbar.Add($"更新失败: {result.ErrorMessage}", Severity.Error);
                // 恢复原值
                await LoadData();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"更新用户部门失败: {ex.Message}", Severity.Error);
            // 恢复原值
            await LoadData();
        }
    }
}
