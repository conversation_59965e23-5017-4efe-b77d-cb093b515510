@page "/equipment-test"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@inject IDepartmentService DepartmentService
@inject IEquipmentModelService EquipmentModelService
@inject ILocationService LocationService
@inject IEquipmentService EquipmentService
@inject IRepairOrderService RepairOrderService
@inject ISnackbar Snackbar

<PageTitle>设备管理功能测试</PageTitle>

<div>
    <MudPaper Class="pa-4">
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h4" Class="mb-4">
                    <MudIcon Icon="Icons.Material.Filled.Science" Class="mr-2" />
                    设备管理功能测试
                </MudText>
            </MudItem>

            <!-- 测试按钮 -->
            <MudItem xs="12">
                <MudStack Row Spacing="2" Class="mb-4">
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Primary" 
                             StartIcon="Icons.Material.Filled.PlayArrow"
                             OnClick="TestDatabaseConnection">
                        测试数据库连接
                    </MudButton>
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Secondary" 
                             StartIcon="Icons.Material.Filled.Add"
                             OnClick="CreateTestData">
                        创建测试数据
                    </MudButton>
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Info" 
                             StartIcon="Icons.Material.Filled.List"
                             OnClick="LoadAllData">
                        加载所有数据
                    </MudButton>
                    <MudButton Variant="Variant.Outlined" 
                             Color="Color.Warning" 
                             StartIcon="Icons.Material.Filled.Clear"
                             OnClick="ClearResults">
                        清空结果
                    </MudButton>
                </MudStack>
            </MudItem>

            <!-- 测试结果 -->
            <MudItem xs="12">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">测试结果</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudTextField @bind-Value="testResults" 
                                    Label="测试输出" 
                                    Lines="20" 
                                    ReadOnly="true"
                                    Variant="Variant.Outlined" />
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- 数据统计 -->
            <MudItem xs="12">
                <MudGrid>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard>
                            <MudCardContent>
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                    <div>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary">部门数量</MudText>
                                        <MudText Typo="Typo.h5">@departmentCount</MudText>
                                    </div>
                                    <MudIcon Icon="Icons.Material.Filled.Business" Color="Color.Primary" Size="Size.Large" />
                                </MudStack>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard>
                            <MudCardContent>
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                    <div>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary">设备型号</MudText>
                                        <MudText Typo="Typo.h5">@equipmentModelCount</MudText>
                                    </div>
                                    <MudIcon Icon="Icons.Material.Filled.Category" Color="Color.Secondary" Size="Size.Large" />
                                </MudStack>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard>
                            <MudCardContent>
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                    <div>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary">位置数量</MudText>
                                        <MudText Typo="Typo.h5">@locationCount</MudText>
                                    </div>
                                    <MudIcon Icon="Icons.Material.Filled.LocationOn" Color="Color.Info" Size="Size.Large" />
                                </MudStack>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard>
                            <MudCardContent>
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                    <div>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary">设备数量</MudText>
                                        <MudText Typo="Typo.h5">@equipmentCount</MudText>
                                    </div>
                                    <MudIcon Icon="Icons.Material.Filled.Devices" Color="Color.Success" Size="Size.Large" />
                                </MudStack>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                </MudGrid>
            </MudItem>

            <!-- 快速导航 -->
            <MudItem xs="12">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">快速导航</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudStack Row Spacing="2" Wrap="Wrap.Wrap">
                            <MudButton Variant="Variant.Outlined" 
                                     StartIcon="Icons.Material.Filled.Business"
                                     Href="/department-management">
                                部门管理
                            </MudButton>
                            <MudButton Variant="Variant.Outlined" 
                                     StartIcon="Icons.Material.Filled.Category"
                                     Href="/equipment-model-management">
                                设备型号管理
                            </MudButton>
                            <MudButton Variant="Variant.Outlined" 
                                     StartIcon="Icons.Material.Filled.LocationOn"
                                     Href="/location-management">
                                位置管理
                            </MudButton>
                            <MudButton Variant="Variant.Outlined" 
                                     StartIcon="Icons.Material.Filled.Devices"
                                     Href="/equipment-management">
                                设备管理
                            </MudButton>
                            <MudButton Variant="Variant.Outlined" 
                                     StartIcon="Icons.Material.Filled.Build"
                                     Href="/create-repair-order">
                                设备报修
                            </MudButton>
                            <MudButton Variant="Variant.Outlined" 
                                     StartIcon="Icons.Material.Filled.Assignment"
                                     Href="/repair-order-management">
                                报修单管理
                            </MudButton>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>
    </MudPaper>
</div>

@code {
    private string testResults = "";
    private int departmentCount = 0;
    private int equipmentModelCount = 0;
    private int locationCount = 0;
    private int equipmentCount = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadAllData();
    }

    private async Task TestDatabaseConnection()
    {
        try
        {
            AppendResult("开始测试数据库连接...");
            
            var departments = await DepartmentService.GetAllDepartmentsAsync();
            AppendResult($"✅ 数据库连接成功！找到 {departments.Count} 个部门");
            
            var models = await EquipmentModelService.GetAllEquipmentModelsAsync();
            AppendResult($"✅ 设备型号表连接成功！找到 {models.Count} 个型号");
            
            AppendResult("🎉 所有数据库连接测试通过！");
            Snackbar.Add("数据库连接测试成功", Severity.Success);
        }
        catch (Exception ex)
        {
            AppendResult($"❌ 数据库连接失败: {ex.Message}");
            Snackbar.Add($"数据库连接失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task CreateTestData()
    {
        try
        {
            AppendResult("开始创建测试数据...");

            // 创建测试部门
            var testDept = new Department
            {
                Code = "TEST001",
                Name = "测试部门",
                Description = "用于测试的部门",
                Level = 1,
                SortOrder = 999,
                IsEnabled = true
            };

            var deptResult = await DepartmentService.CreateDepartmentAsync(testDept);
            if (deptResult.IsSuccess)
            {
                AppendResult("✅ 测试部门创建成功");
            }
            else
            {
                AppendResult($"⚠️ 测试部门创建失败或已存在: {deptResult.ErrorMessage}");
            }

            // 创建测试设备型号
            var testModel = new EquipmentModel
            {
                Code = "MODEL001",
                Name = "测试设备型号",
                Category = "测试类别",
                Brand = "测试品牌",
                Description = "用于测试的设备型号",
                IsEnabled = true
            };

            var modelResult = await EquipmentModelService.CreateEquipmentModelAsync(testModel);
            if (modelResult.IsSuccess)
            {
                AppendResult("✅ 测试设备型号创建成功");
            }
            else
            {
                AppendResult($"⚠️ 测试设备型号创建失败或已存在: {modelResult.ErrorMessage}");
            }

            AppendResult("🎉 测试数据创建完成！");
            Snackbar.Add("测试数据创建成功", Severity.Success);
            
            await LoadAllData();
        }
        catch (Exception ex)
        {
            AppendResult($"❌ 创建测试数据失败: {ex.Message}");
            Snackbar.Add($"创建测试数据失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadAllData()
    {
        try
        {
            AppendResult("开始加载所有数据统计...");

            var departments = await DepartmentService.GetAllDepartmentsAsync();
            departmentCount = departments.Count;
            AppendResult($"📊 部门数量: {departmentCount}");

            var models = await EquipmentModelService.GetAllEquipmentModelsAsync();
            equipmentModelCount = models.Count;
            AppendResult($"📊 设备型号数量: {equipmentModelCount}");

            var locations = await LocationService.GetAllLocationsAsync();
            locationCount = locations.Count;
            AppendResult($"📊 位置数量: {locationCount}");

            var equipment = await EquipmentService.GetAllEquipmentAsync();
            equipmentCount = equipment.Count;
            AppendResult($"📊 设备数量: {equipmentCount}");

            AppendResult("✅ 数据统计加载完成");
            StateHasChanged();
        }
        catch (Exception ex)
        {
            AppendResult($"❌ 加载数据失败: {ex.Message}");
            Snackbar.Add($"加载数据失败: {ex.Message}", Severity.Error);
        }
    }

    private void ClearResults()
    {
        testResults = "";
        StateHasChanged();
    }

    private void AppendResult(string message)
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss");
        testResults += $"[{timestamp}] {message}\n";
        StateHasChanged();
    }
}
