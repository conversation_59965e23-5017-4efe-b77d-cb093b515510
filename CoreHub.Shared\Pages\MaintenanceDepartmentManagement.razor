@page "/maintenance-department-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@using MudBlazor
@inject IMaintenanceDepartmentPermissionService MaintenanceDepartmentPermissionService
@inject IDepartmentService DepartmentService
@inject IDepartmentTypeService DepartmentTypeService
@inject AuthenticationStateProvider AuthStateProvider
@inject IUserManagementService UserManagementService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>维修部门管理</PageTitle>

<AuthorizeView>
    <Authorized>
        @if (!hasManagementPermission)
        {
            <div>
                <MudPaper Class="pa-6">
                    <MudStack AlignItems="AlignItems.Center" Spacing="4">
                        <MudIcon Icon="@Icons.Material.Filled.Security" Size="Size.Large" Color="Color.Warning" />
                        <MudText Typo="Typo.h5">权限不足</MudText>
                        <MudText Typo="Typo.body1" Color="Color.Secondary">您没有权限管理维修部门权限配置，请联系管理员</MudText>
                        <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="/" StartIcon="@Icons.Material.Filled.Home">
                            返回首页
                        </MudButton>
                    </MudStack>
                </MudPaper>
            </div>
        }
        else
        {
            <div>
                <MudPaper Class="pa-6">
                    <MudGrid>
                        <!-- 页面标题 -->
                        <MudItem xs="12">
                            <MudStack Row AlignItems="AlignItems.Center" Spacing="3" Class="mb-6">
                                <MudIcon Icon="@Icons.Material.Filled.Engineering" Size="Size.Large" Color="Color.Primary" />
                                <div>
                                    <MudText Typo="Typo.h4">维修部门管理</MudText>
                                    <MudText Typo="Typo.body1" Color="Color.Secondary">
                                        管理维修部门与生产部门之间的维修权限关系，设置哪些维修部门可以维修哪些生产部门的设备
                                    </MudText>
                                </div>
                                <MudSpacer />
                                <MudButton Variant="Variant.Outlined" Color="Color.Primary"
                                    StartIcon="@Icons.Material.Filled.Refresh" OnClick="LoadData">
                                    刷新数据
                                </MudButton>
                            </MudStack>
                        </MudItem>

                        <!-- 统计信息 -->
                        <MudItem xs="12">
                            <MudGrid>
                                <MudItem xs="6" sm="3">
                                    <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                            <MudIcon Icon="@Icons.Material.Filled.Engineering" Size="Size.Large" />
                                            <div>
                                                <MudText Typo="Typo.h4">@maintenanceDepartments.Count</MudText>
                                                <MudText Typo="Typo.body2">维修部门</MudText>
                                            </div>
                                        </MudStack>
                                    </MudPaper>
                                </MudItem>
                                <MudItem xs="6" sm="3">
                                    <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                            <MudIcon Icon="@Icons.Material.Filled.Factory" Size="Size.Large" />
                                            <div>
                                                <MudText Typo="Typo.h4">@productionDepartments.Count</MudText>
                                                <MudText Typo="Typo.body2">生产部门</MudText>
                                            </div>
                                        </MudStack>
                                    </MudPaper>
                                </MudItem>
                                <MudItem xs="6" sm="3">
                                    <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                            <MudIcon Icon="@Icons.Material.Filled.Link" Size="Size.Large" />
                                            <div>
                                                <MudText Typo="Typo.h4">@GetTotalPermissions()</MudText>
                                                <MudText Typo="Typo.body2">权限配置</MudText>
                                            </div>
                                        </MudStack>
                                    </MudPaper>
                                </MudItem>
                                <MudItem xs="6" sm="3">
                                    <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                            <MudIcon Icon="@Icons.Material.Filled.Percent" Size="Size.Large" />
                                            <div>
                                                <MudText Typo="Typo.h4">@GetCoveragePercentage()%</MudText>
                                                <MudText Typo="Typo.body2">覆盖率</MudText>
                                            </div>
                                        </MudStack>
                                    </MudPaper>
                                </MudItem>
                            </MudGrid>
                        </MudItem>

                    <!-- 维修部门权限分配 -->
                    <MudItem xs="12">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">维修权限分配</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <!-- 维修部门选择 -->
                                    <MudItem xs="12" md="4">
                                        <MudSelect T="Department" @bind-Value="selectedMaintenanceDepartment"
                                            Label="选择维修部门" Variant="Variant.Outlined" AnchorOrigin="Origin.BottomCenter"
                                            ToStringFunc="@(d => d?.Name ?? "")">
                                            @foreach (var dept in maintenanceDepartments)
                                            {
                                                <MudSelectItem Value="@dept">
                                                    <div class="d-flex align-center justify-space-between">
                                                        <div class="d-flex align-center">
                                                            <MudIcon Icon="@Icons.Material.Filled.Engineering" Size="Size.Small" Class="mr-2" />
                                                            <span>@dept.Name</span>
                                                        </div>
                                                        <MudChip T="string" Color="Color.Info" Size="Size.Small">
                                                            @GetCurrentSelectionCount(dept.Id)/@productionDepartments.Count
                                                        </MudChip>
                                                    </div>
                                                </MudSelectItem>
                                            }
                                        </MudSelect>
                                    </MudItem>

                                    <!-- 快速操作 -->
                                    <MudItem xs="12" md="8">
                                        @if (selectedMaintenanceDepartment != null)
                                        {
                                            <MudStack Row Spacing="2" AlignItems="AlignItems.Center">
                                                <MudButton Variant="Variant.Filled" Color="Color.Success" Size="Size.Small"
                                                    OnClick="@(() => SelectAllDepartments(selectedMaintenanceDepartment.Id))"
                                                    StartIcon="@Icons.Material.Filled.SelectAll"
                                                    Disabled="@(!hasManagementPermission)">
                                                    全选
                                                </MudButton>
                                                <MudButton Variant="Variant.Filled" Color="Color.Warning" Size="Size.Small"
                                                    OnClick="@(() => ClearSelections(selectedMaintenanceDepartment.Id))"
                                                    StartIcon="@Icons.Material.Filled.Clear"
                                                    Disabled="@(!hasManagementPermission || GetCurrentSelectionCount(selectedMaintenanceDepartment.Id) == 0)">
                                                    清空
                                                </MudButton>
                                                <MudButton Variant="Variant.Filled" Color="Color.Primary" Size="Size.Small"
                                                    OnClick="SaveMaintenancePermissions" StartIcon="@Icons.Material.Filled.Save"
                                                    Disabled="@(saving || !hasManagementPermission)">
                                                    @if (saving)
                                                    {
                                                        <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                                                        <MudText Class="ms-2">保存中...</MudText>
                                                    }
                                                    else
                                                    {
                                                        <MudText>保存配置</MudText>
                                                    }
                                                </MudButton>
                                                <MudSpacer />
                                                <MudText Typo="Typo.body2" Color="Color.Secondary">
                                                    已分配 @GetCurrentSelectionCount(selectedMaintenanceDepartment.Id) 个生产部门
                                                </MudText>
                                            </MudStack>
                                        }
                                    </MudItem>
                                    <!-- 部门分配列表 -->
                                    <MudItem xs="12">
                                        @if (selectedMaintenanceDepartment != null)
                                        {
                                            <MudText Typo="Typo.subtitle1" Class="mb-3">
                                                为维修部门 "@selectedMaintenanceDepartment.Name" 分配可维修的生产部门
                                            </MudText>
                                            <MudGrid>
                                                @foreach (var dept in productionDepartments)
                                                {
                                                    <MudItem xs="6" sm="4" md="3">
                                                        <MudCheckBox T="bool"
                                                            Value="@IsMaintenancePermissionAssigned(selectedMaintenanceDepartment.Id, dept.Id)"
                                                            ValueChanged="@((bool isChecked) => OnMaintenancePermissionChanged(selectedMaintenanceDepartment.Id, dept.Id, isChecked))"
                                                            Label="@dept.Name"
                                                            Color="Color.Primary"
                                                            Disabled="@(!hasManagementPermission)" />
                                                    </MudItem>
                                                }
                                            </MudGrid>
                                        }
                                        else
                                        {
                                            <MudAlert Severity="Severity.Info">
                                                请选择一个维修部门来管理其可维修的生产部门
                                            </MudAlert>
                                        }
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>

                    <!-- 权限矩阵视图 -->
                    <MudItem xs="12">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">权限矩阵视图</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                @if (loading)
                                {
                                    <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="my-7" />
                                }
                                else if (permissionMatrix.Any())
                                {
                                    <MudTable Items="@permissionMatrix" Hover="true" Dense="true" Striped="true" FixedHeader="true" Height="400px">
                                        <HeaderContent>
                                            <MudTh Style="min-width: 120px;">维修部门</MudTh>
                                            @foreach (var dept in productionDepartments)
                                            {
                                                <MudTh Style="text-align: center; min-width: 80px;">
                                                    <MudText Typo="Typo.caption">@dept.Name</MudText>
                                                </MudTh>
                                            }
                                            <MudTh Style="text-align: center; min-width: 100px;">权限统计</MudTh>
                                        </HeaderContent>
                                        <RowTemplate Context="matrixRow">
                                            <MudTd DataLabel="维修部门">
                                                <MudChip T="string" Color="Color.Primary" Size="Size.Small">
                                                    @matrixRow.MaintenanceDepartmentName
                                                </MudChip>
                                            </MudTd>
                                            @foreach (var dept in productionDepartments)
                                            {
                                                <MudTd DataLabel="@dept.Name" Style="text-align: center;">
                                                    @{
                                                        var hasPermission = matrixRow.TargetDepartmentIds.Contains(dept.Id);
                                                    }
                                                    @if (hasPermission)
                                                    {
                                                        <MudIcon Icon="@Icons.Material.Filled.CheckCircle"
                                                            Color="Color.Success" Size="Size.Small" />
                                                    }
                                                    else
                                                    {
                                                        <MudIcon Icon="@Icons.Material.Filled.RadioButtonUnchecked"
                                                            Color="Color.Default" Size="Size.Small" />
                                                    }
                                                </MudTd>
                                            }
                                            <MudTd DataLabel="权限统计" Style="text-align: center;">
                                                <MudChip T="string" Color="@(matrixRow.TargetDepartmentIds.Count > 0 ? Color.Info : Color.Default)"
                                                    Size="Size.Small">
                                                    @matrixRow.TargetDepartmentIds.Count/@productionDepartments.Count
                                                </MudChip>
                                            </MudTd>
                                        </RowTemplate>
                                    </MudTable>
                                }
                                else
                                {
                                    <MudAlert Severity="Severity.Warning">
                                        暂无维修权限配置数据
                                    </MudAlert>
                                }
                            </MudCardContent>
                        </MudCard>
                    </MudItem>

                    <!-- 权限详情列表 -->
                    <MudItem xs="12">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">权限详情</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudTable Items="@allPermissions" Hover="true" Dense="true" Striped="true">
                                    <HeaderContent>
                                        <MudTh>维修部门</MudTh>
                                        <MudTh>目标部门</MudTh>
                                        <MudTh>状态</MudTh>
                                        <MudTh>创建时间</MudTh>
                                        <MudTh>备注</MudTh>
                                        <MudTh>操作</MudTh>
                                    </HeaderContent>
                                    <RowTemplate Context="permissionItem">
                                        <MudTd DataLabel="维修部门">
                                            <div class="d-flex align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.Engineering" Size="Size.Small" Class="mr-2" />
                                                <span>@permissionItem.MaintenanceDepartment?.Name</span>
                                            </div>
                                        </MudTd>
                                        <MudTd DataLabel="目标部门">
                                            <div class="d-flex align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.Factory" Size="Size.Small" Class="mr-2" />
                                                <span>@permissionItem.TargetDepartment?.Name</span>
                                            </div>
                                        </MudTd>
                                        <MudTd DataLabel="状态">
                                            <MudChip T="string" Color="@(permissionItem.IsEnabled ? Color.Success : Color.Default)"
                                                Size="Size.Small">
                                                @(permissionItem.IsEnabled ? "启用" : "禁用")
                                            </MudChip>
                                        </MudTd>
                                        <MudTd DataLabel="创建时间">
                                            <MudText Typo="Typo.caption">
                                                @permissionItem.CreatedAt.ToString("yyyy-MM-dd HH:mm")
                                            </MudText>
                                        </MudTd>
                                        <MudTd DataLabel="备注">
                                            <MudText Typo="Typo.caption">
                                                @(string.IsNullOrEmpty(permissionItem.Remark) ? "-" : permissionItem.Remark)
                                            </MudText>
                                        </MudTd>
                                        <MudTd DataLabel="操作">
                                            <MudButtonGroup Size="Size.Small" Variant="Variant.Text">
                                                <MudButton Color="@(permissionItem.IsEnabled ? Color.Warning : Color.Success)"
                                                    StartIcon="@(permissionItem.IsEnabled ? Icons.Material.Filled.ToggleOff : Icons.Material.Filled.ToggleOn)"
                                                    OnClick="@(() => TogglePermissionStatus(permissionItem.Id, !permissionItem.IsEnabled))"
                                                    Disabled="@(!hasManagementPermission)">
                                                    @(permissionItem.IsEnabled ? "禁用" : "启用")
                                                </MudButton>
                                                <MudButton Color="Color.Error"
                                                    StartIcon="@Icons.Material.Filled.Delete"
                                                    OnClick="@(() => DeletePermission(permissionItem.MaintenanceDepartmentId, permissionItem.TargetDepartmentId))"
                                                    Disabled="@(!hasManagementPermission)">
                                                    删除
                                                </MudButton>
                                            </MudButtonGroup>
                                        </MudTd>
                                    </RowTemplate>
                                </MudTable>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>

                    <!-- 统计信息 -->
                    <MudItem xs="12">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">统计信息</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <MudItem xs="6" sm="3">
                                        <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                                <MudIcon Icon="@Icons.Material.Filled.Engineering" Size="Size.Large" />
                                                <div>
                                                    <MudText Typo="Typo.h4">@maintenanceDepartments.Count</MudText>
                                                    <MudText Typo="Typo.body2">维修部门数</MudText>
                                                </div>
                                            </MudStack>
                                        </MudPaper>
                                    </MudItem>
                                    <MudItem xs="6" sm="3">
                                        <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                                            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                                <MudIcon Icon="@Icons.Material.Filled.Factory" Size="Size.Large" />
                                                <div>
                                                    <MudText Typo="Typo.h4">@productionDepartments.Count</MudText>
                                                    <MudText Typo="Typo.body2">生产部门数</MudText>
                                                </div>
                                            </MudStack>
                                        </MudPaper>
                                    </MudItem>
                                    <MudItem xs="6" sm="3">
                                        <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                                            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                                <MudIcon Icon="@Icons.Material.Filled.Link" Size="Size.Large" />
                                                <div>
                                                    <MudText Typo="Typo.h4">@GetTotalPermissions()</MudText>
                                                    <MudText Typo="Typo.body2">权限关系数</MudText>
                                                </div>
                                            </MudStack>
                                        </MudPaper>
                                    </MudItem>
                                    <MudItem xs="6" sm="3">
                                        <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                                            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                                <MudIcon Icon="@Icons.Material.Filled.Percent" Size="Size.Large" />
                                                <div>
                                                    <MudText Typo="Typo.h4">@GetCoveragePercentage()%</MudText>
                                                    <MudText Typo="Typo.body2">权限覆盖率</MudText>
                                                </div>
                                            </MudStack>
                                        </MudPaper>
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    </MudGrid>
                </MudPaper>
            </div>
        }
    </Authorized>
    <NotAuthorized>
        <div>
            <MudPaper Class="pa-6">
                <MudStack AlignItems="AlignItems.Center" Spacing="4">
                    <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Large" Color="Color.Warning" />
                    <MudText Typo="Typo.h5">需要登录</MudText>
                    <MudText Typo="Typo.body1" Color="Color.Secondary">请先登录后再访问此页面</MudText>
                </MudStack>
            </MudPaper>
        </div>
    </NotAuthorized>
</AuthorizeView>

@code {
    private List<Department> maintenanceDepartments = new();
    private List<Department> productionDepartments = new();
    private List<MaintenanceDepartmentPermission> allPermissions = new();
    private List<PermissionMatrixRow> permissionMatrix = new();
    private Dictionary<int, HashSet<int>> maintenancePermissions = new(); // MaintenanceDepartmentId -> Set of TargetDepartmentIds
    private Department? selectedMaintenanceDepartment;
    private bool loading = false;
    private bool saving = false;
    private bool hasManagementPermission = false;
    private int currentUserId = 0;

    protected override async Task OnInitializedAsync()
    {
        await CheckUserPermissions();
        if (hasManagementPermission)
        {
            await LoadData();
        }
    }

    private async Task CheckUserPermissions()
    {
        try
        {
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                // 尝试多种方式获取用户ID
                var userIdClaim = authState.User.FindFirst("UserId") ?? authState.User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim != null && int.TryParse(userIdClaim.Value, out currentUserId))
                {
                    Snackbar.Add($"成功获取用户ID: {currentUserId}", Severity.Success);

                    // 检查是否为超级管理员
                    var user = await UserManagementService.GetUserByIdAsync(currentUserId);
                    if (user != null && user.Username.ToLower() == "admin")
                    {
                        hasManagementPermission = true;
                        Snackbar.Add("检测到超级管理员权限", Severity.Success);
                        return;
                    }

                    // 检查是否有维修部门管理权限
                    hasManagementPermission = await UserManagementService.CheckUserPermissionAsync(currentUserId, "MaintenanceDepartment.Manage");

                    if (hasManagementPermission)
                    {
                        Snackbar.Add("您有维修部门管理权限", Severity.Success);
                    }
                    else
                    {
                        Snackbar.Add("您没有权限管理维修部门权限配置", Severity.Warning);
                    }
                }
                else
                {
                    // 调试信息：显示所有可用的 Claims
                    var allClaims = string.Join(", ", authState.User.Claims.Select(c => $"{c.Type}={c.Value}"));
                    Snackbar.Add($"无法获取用户ID。可用Claims: {allClaims}", Severity.Warning);
                }
            }
            else
            {
                Snackbar.Add("用户未认证", Severity.Warning);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"权限验证失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadData()
    {
        loading = true;
        try
        {
            // 获取维修类型的部门
            maintenanceDepartments = await DepartmentTypeService.GetMaintenanceDepartmentsAsync();

            // 加载部门类型信息
            foreach (var dept in maintenanceDepartments)
            {
                if (dept.DepartmentTypeId.HasValue)
                {
                    dept.DepartmentType = await DepartmentTypeService.GetDepartmentTypeByIdAsync(dept.DepartmentTypeId.Value);
                }
            }

            // 获取生产类型的部门
            productionDepartments = await DepartmentTypeService.GetProductionDepartmentsAsync();

            await LoadMaintenancePermissions();
            await LoadAllPermissions();
            BuildPermissionMatrix();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载数据失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private async Task LoadMaintenancePermissions()
    {
        maintenancePermissions.Clear();

        var permissionMatrix = await MaintenanceDepartmentPermissionService.GetMaintenanceDepartmentPermissionMatrixAsync();

        foreach (var permission in permissionMatrix)
        {
            maintenancePermissions[permission.Key] = new HashSet<int>(permission.Value);
        }
    }

    private async Task LoadAllPermissions()
    {
        allPermissions = await MaintenanceDepartmentPermissionService.GetAllMaintenanceDepartmentPermissionsAsync();
    }

    private void BuildPermissionMatrix()
    {
        permissionMatrix.Clear();

        foreach (var maintenanceDept in maintenanceDepartments)
        {
            var matrixRow = new PermissionMatrixRow
            {
                MaintenanceDepartmentId = maintenanceDept.Id,
                MaintenanceDepartmentName = maintenanceDept.Name,
                TargetDepartmentIds = maintenancePermissions.GetValueOrDefault(maintenanceDept.Id, new HashSet<int>()).ToList()
            };
            permissionMatrix.Add(matrixRow);
        }
    }

    private bool IsMaintenancePermissionAssigned(int maintenanceDepartmentId, int targetDepartmentId)
    {
        return maintenancePermissions.GetValueOrDefault(maintenanceDepartmentId, new HashSet<int>()).Contains(targetDepartmentId);
    }

    private void OnMaintenancePermissionChanged(int maintenanceDepartmentId, int targetDepartmentId, bool isAssigned)
    {
        if (!maintenancePermissions.ContainsKey(maintenanceDepartmentId))
        {
            maintenancePermissions[maintenanceDepartmentId] = new HashSet<int>();
        }

        if (isAssigned)
        {
            maintenancePermissions[maintenanceDepartmentId].Add(targetDepartmentId);
            var deptName = productionDepartments.FirstOrDefault(d => d.Id == targetDepartmentId)?.Name ?? $"部门{targetDepartmentId}";
            Snackbar.Add($"已添加权限：{deptName}", Severity.Success);
        }
        else
        {
            maintenancePermissions[maintenanceDepartmentId].Remove(targetDepartmentId);
            var deptName = productionDepartments.FirstOrDefault(d => d.Id == targetDepartmentId)?.Name ?? $"部门{targetDepartmentId}";
            Snackbar.Add($"已移除权限：{deptName}", Severity.Warning);
        }

        // 强制刷新UI
        StateHasChanged();
    }

    private void ClearSelections(int maintenanceDepartmentId)
    {
        if (maintenancePermissions.ContainsKey(maintenanceDepartmentId))
        {
            maintenancePermissions[maintenanceDepartmentId].Clear();
            Snackbar.Add("已清空所有选择", Severity.Info);
            StateHasChanged();
        }
    }

    private void SelectAllDepartments(int maintenanceDepartmentId)
    {
        if (!maintenancePermissions.ContainsKey(maintenanceDepartmentId))
        {
            maintenancePermissions[maintenanceDepartmentId] = new HashSet<int>();
        }

        foreach (var dept in productionDepartments)
        {
            maintenancePermissions[maintenanceDepartmentId].Add(dept.Id);
        }

        Snackbar.Add($"已选择所有 {productionDepartments.Count} 个生产部门", Severity.Success);
        StateHasChanged();
    }

    private int GetCurrentSelectionCount(int maintenanceDepartmentId)
    {
        return maintenancePermissions.GetValueOrDefault(maintenanceDepartmentId, new HashSet<int>()).Count;
    }

    private string GetSelectedDepartmentNames(int maintenanceDepartmentId)
    {
        var currentSelections = maintenancePermissions.GetValueOrDefault(maintenanceDepartmentId, new HashSet<int>());
        var selectedDeptNames = productionDepartments.Where(d => currentSelections.Contains(d.Id)).Select(d => d.Name);
        return string.Join(", ", selectedDeptNames);
    }

    private async Task SaveMaintenancePermissions()
    {
        if (selectedMaintenanceDepartment == null) return;

        // 验证权限
        if (!hasManagementPermission)
        {
            Snackbar.Add("您没有权限执行此操作", Severity.Warning);
            return;
        }

        saving = true;
        try
        {
            var targetDepartmentIds = maintenancePermissions.GetValueOrDefault(selectedMaintenanceDepartment.Id, new HashSet<int>()).ToList();

            Snackbar.Add($"准备保存：维修部门ID={selectedMaintenanceDepartment.Id}，目标部门IDs=[{string.Join(",", targetDepartmentIds)}]", Severity.Info);

            var result = await MaintenanceDepartmentPermissionService.SetMaintenanceDepartmentPermissionsAsync(selectedMaintenanceDepartment.Id, targetDepartmentIds);

            if (result)
            {
                Snackbar.Add($"维修部门 {selectedMaintenanceDepartment.Name} 的权限已保存", Severity.Success);

                // 添加短暂延迟确保数据库事务完全提交
                await Task.Delay(500);

                // 重新加载数据
                await LoadMaintenancePermissions();
                await LoadAllPermissions();
                BuildPermissionMatrix();

                // 验证保存结果
                var savedPermissions = maintenancePermissions.GetValueOrDefault(selectedMaintenanceDepartment.Id, new HashSet<int>());
                Snackbar.Add($"验证：当前已保存 {savedPermissions.Count} 个权限关系", Severity.Info);
            }
            else
            {
                Snackbar.Add("保存失败", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"保存失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            saving = false;
        }
    }

    private async Task TogglePermissionStatus(int permissionId, bool isEnabled)
    {
        // 验证权限
        if (!hasManagementPermission)
        {
            Snackbar.Add("您没有权限执行此操作", Severity.Warning);
            return;
        }

        try
        {
            var result = await MaintenanceDepartmentPermissionService.ToggleMaintenanceDepartmentPermissionAsync(permissionId, isEnabled);

            if (result)
            {
                Snackbar.Add($"权限状态已{(isEnabled ? "启用" : "禁用")}", Severity.Success);
                await LoadAllPermissions();
                await LoadMaintenancePermissions();
                BuildPermissionMatrix();
            }
            else
            {
                Snackbar.Add("操作失败", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task DeletePermission(int maintenanceDepartmentId, int targetDepartmentId)
    {
        // 验证权限
        if (!hasManagementPermission)
        {
            Snackbar.Add("您没有权限执行此操作", Severity.Warning);
            return;
        }

        var confirmed = await DialogService.ShowMessageBox(
            "确认删除",
            "确定要删除这个维修权限关系吗？",
            yesText: "删除", cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                var result = await MaintenanceDepartmentPermissionService.DeleteMaintenanceDepartmentPermissionAsync(maintenanceDepartmentId, targetDepartmentId);

                if (result)
                {
                    Snackbar.Add("权限关系已删除", Severity.Success);
                    await LoadAllPermissions();
                    await LoadMaintenancePermissions();
                    BuildPermissionMatrix();
                }
                else
                {
                    Snackbar.Add("删除失败", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private int GetTotalPermissions()
    {
        return allPermissions.Count(p => p.IsEnabled);
    }

    private int GetCoveragePercentage()
    {
        if (maintenanceDepartments.Count == 0 || productionDepartments.Count == 0) return 0;
        var totalPossible = maintenanceDepartments.Count * productionDepartments.Count;
        var totalAssigned = GetTotalPermissions();
        return (int)Math.Round((double)totalAssigned / totalPossible * 100);
    }

    public class PermissionMatrixRow
    {
        public int MaintenanceDepartmentId { get; set; }
        public string MaintenanceDepartmentName { get; set; } = string.Empty;
        public List<int> TargetDepartmentIds { get; set; } = new();
    }
}
