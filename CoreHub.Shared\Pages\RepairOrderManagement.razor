@page "/repair-order-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject IRepairOrderService RepairOrderService
@inject IRepairWorkflowService RepairWorkflowService
@inject IDepartmentService DepartmentService
@inject IUserManagementService UserManagementService
@inject ISnackbar Snackbar
@inject IDialogService DialogService
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthStateProvider

<PageTitle>报修单管理</PageTitle>

<AuthorizeView Context="authContext">
    <Authorized>
        <div>
    <MudPaper Class="pa-4">
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h4" Class="mb-4">
                    <MudIcon Icon="@Icons.Material.Filled.Assignment" Class="mr-2" />
                    报修单管理
                </MudText>
            </MudItem>

            <!-- 统计卡片 -->
            <MudItem xs="12" Class="mb-4">
                <MudGrid>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard>
                            <MudCardContent>
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                    <div>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary">总报修单</MudText>
                                        <MudText Typo="Typo.h5">@statistics.TotalCount</MudText>
                                    </div>
                                    <MudIcon Icon="@Icons.Material.Filled.Assignment" Color="Color.Primary" Size="Size.Large" />
                                </MudStack>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard>
                            <MudCardContent>
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                    <div>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary">待处理</MudText>
                                        <MudText Typo="Typo.h5" Color="Color.Warning">@statistics.PendingCount</MudText>
                                    </div>
                                    <MudIcon Icon="@Icons.Material.Filled.Schedule" Color="Color.Warning" Size="Size.Large" />
                                </MudStack>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard>
                            <MudCardContent>
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                    <div>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary">处理中</MudText>
                                        <MudText Typo="Typo.h5" Color="Color.Info">@statistics.InProgressCount</MudText>
                                    </div>
                                    <MudIcon Icon="@Icons.Material.Filled.Build" Color="Color.Info" Size="Size.Large" />
                                </MudStack>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard>
                            <MudCardContent>
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                    <div>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary">紧急报修</MudText>
                                        <MudText Typo="Typo.h5" Color="Color.Error">@statistics.UrgentCount</MudText>
                                    </div>
                                    <MudIcon Icon="@Icons.Material.Filled.PriorityHigh" Color="Color.Error" Size="Size.Large" />
                                </MudStack>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                </MudGrid>
            </MudItem>

            <!-- 搜索和过滤 -->
            <MudItem xs="12">
                <MudExpansionPanels>
                    <MudExpansionPanel Text="搜索和过滤">
                        <MudGrid>
                            <MudItem xs="12" md="4">
                                <MudTextField @bind-Value="searchDto.SearchText" 
                                            Label="搜索关键词" 
                                            Placeholder="报修单号、设备名称、故障描述..."
                                            Adornment="Adornment.Start" 
                                            AdornmentIcon="@Icons.Material.Filled.Search" />
                            </MudItem>
                            <MudItem xs="12" md="4">
                                <MudSelect T="int?" @bind-Value="searchDto.Status"
                                         Label="报修状态"
                                         Clearable="true">
                                    @foreach (var status in CoreHub.Shared.Utils.RepairOrderStatusHelper.GetAllStatuses())
                                    {
                                        <MudSelectItem T="int?" Value="status.Value">@status.Name</MudSelectItem>
                                    }
                                    <MudSelectItem T="int?" Value="8">待确认</MudSelectItem>
                                </MudSelect>
                            </MudItem>
                            <MudItem xs="12" md="4">
                                <MudSelect T="int?" @bind-Value="searchDto.UrgencyLevel"
                                         Label="紧急程度"
                                         Clearable="true">
                                    <MudSelectItem T="int?" Value="1">紧急</MudSelectItem>
                                    <MudSelectItem T="int?" Value="2">高</MudSelectItem>
                                    <MudSelectItem T="int?" Value="3">中</MudSelectItem>
                                    <MudSelectItem T="int?" Value="4">低</MudSelectItem>
                                </MudSelect>
                            </MudItem>
                            <MudItem xs="12" md="4">
                                <MudSelect T="int?" @bind-Value="searchDto.MaintenanceDepartmentId"
                                         Label="维修部门"
                                         Clearable="true">
                                    @foreach (var dept in departments)
                                    {
                                        <MudSelectItem T="int?" Value="@(dept.Id)">@dept.Name</MudSelectItem>
                                    }
                                </MudSelect>
                            </MudItem>
                            <MudItem xs="12" md="4">
                                <MudDatePicker @bind-Date="searchDto.CreatedDateFrom" 
                                             Label="创建日期从" 
                                             Clearable="true" />
                            </MudItem>
                            <MudItem xs="12" md="4">
                                <MudDatePicker @bind-Date="searchDto.CreatedDateTo" 
                                             Label="创建日期到" 
                                             Clearable="true" />
                            </MudItem>
                            <MudItem xs="12">
                                <MudStack Row Spacing="2">
                                    <MudButton Variant="Variant.Filled" 
                                             Color="Color.Primary" 
                                             StartIcon="@Icons.Material.Filled.Search"
                                             OnClick="SearchRepairOrders">
                                        搜索
                                    </MudButton>
                                    <MudButton Variant="Variant.Outlined" 
                                             StartIcon="@Icons.Material.Filled.Clear"
                                             OnClick="ClearSearch">
                                        清空
                                    </MudButton>
                                    <MudButton Variant="Variant.Outlined" 
                                             StartIcon="@Icons.Material.Filled.FilterList"
                                             OnClick="ShowMyRepairOrders">
                                        我的报修
                                    </MudButton>
                                </MudStack>
                            </MudItem>
                        </MudGrid>
                    </MudExpansionPanel>
                </MudExpansionPanels>
            </MudItem>

            <!-- 工具栏 -->
            <MudItem xs="12">
                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-4">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudButton Variant="Variant.Outlined" 
                                 StartIcon="@Icons.Material.Filled.Refresh"
                                 OnClick="LoadRepairOrders">
                            刷新
                        </MudButton>
                        <MudButton Variant="Variant.Outlined" 
                                 StartIcon="@Icons.Material.Filled.FileDownload"
                                 OnClick="ExportRepairOrders">
                            导出
                        </MudButton>
                    </MudStack>
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Primary" 
                             StartIcon="@Icons.Material.Filled.Add"
                             OnClick="CreateRepairOrder">
                        新建报修
                    </MudButton>
                </MudStack>
            </MudItem>

            <!-- 数据表格 -->
            <MudItem xs="12">
                <MudDataGrid T="RepairOrderDetailDto" 
                           Items="@filteredRepairOrders" 
                           Loading="@loading"
                           Hover="true" 
                           Striped="true"
                           Dense="true"
                           FixedHeader="true"
                           Height="600px">
                    <Columns>
                        <PropertyColumn Property="x => x.OrderNumber" Title="报修单号" />
                        <PropertyColumn Property="x => x.EquipmentName" Title="设备名称" />
                        <PropertyColumn Property="x => x.EquipmentDepartmentName" Title="设备部门" />
                        <PropertyColumn Property="x => x.ReporterName" Title="报修人" />
                        <TemplateColumn Title="紧急程度" Sortable="false">
                            <CellTemplate>
                                <MudChip T="string" Color="@GetUrgencyColor(context.Item.UrgencyLevel)"
                                       Size="Size.Small">
                                    @context.Item.UrgencyLevelName
                                </MudChip>
                            </CellTemplate>
                        </TemplateColumn>
                        <TemplateColumn Title="状态" Sortable="false">
                            <CellTemplate>
                                <MudChip T="string" Color="@GetStatusColor(context.Item.Status)"
                                       Size="Size.Small">
                                    @context.Item.StatusName
                                </MudChip>
                            </CellTemplate>
                        </TemplateColumn>
                        <PropertyColumn Property="x => x.MaintenanceDepartmentName" Title="维修部门" />
                        <PropertyColumn Property="x => x.AssignedToName" Title="指派给" />
                        <PropertyColumn Property="x => x.CreatedAt" Title="创建时间" Format="yyyy-MM-dd HH:mm" />
                        <TemplateColumn Title="操作" Sortable="false" Filterable="false">
                            <CellTemplate>
                                <MudStack Row Spacing="1">
                                    <MudIconButton Icon="@Icons.Material.Filled.Visibility" 
                                                 Color="Color.Info" 
                                                 Size="Size.Small"
                                                 OnClick="() => ViewRepairOrderDetail(context.Item)"
                                                 Title="查看详情" />
                                    @if (CanEditRepairOrder(context.Item))
                                    {
                                        <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                                     Color="Color.Primary" 
                                                     Size="Size.Small"
                                                     OnClick="() => EditRepairOrder(context.Item)"
                                                     Title="编辑" />
                                    }
                                    @if (CanCancelRepairOrder(context.Item))
                                    {
                                        <MudIconButton Icon="@Icons.Material.Filled.Cancel" 
                                                     Color="Color.Warning" 
                                                     Size="Size.Small"
                                                     OnClick="() => CancelRepairOrder(context.Item)"
                                                     Title="作废" />
                                    }
                                    @if (CanDeleteRepairOrder(context.Item))
                                    {
                                        <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                     Color="Color.Error"
                                                     Size="Size.Small"
                                                     OnClick="() => DeleteRepairOrder(context.Item)"
                                                     Title="删除" />
                                    }
                                    @if (CanConfirmRepair(context.Item))
                                    {
                                        <MudIconButton Icon="@Icons.Material.Filled.CheckCircle"
                                                     Color="Color.Success"
                                                     Size="Size.Small"
                                                     OnClick="() => ConfirmRepair(context.Item)"
                                                     Title="确认完成" />
                                        <MudIconButton Icon="@Icons.Material.Filled.Refresh"
                                                     Color="Color.Warning"
                                                     Size="Size.Small"
                                                     OnClick="() => RequestRework(context.Item)"
                                                     Title="要求重修" />
                                    }
                                </MudStack>
                            </CellTemplate>
                        </TemplateColumn>
                    </Columns>
                    <NoRecordsContent>
                        <MudText Typo="Typo.body1" Align="Align.Center" Class="pa-4">
                            暂无数据
                        </MudText>
                    </NoRecordsContent>
                    <LoadingContent>
                        <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                    </LoadingContent>
                </MudDataGrid>
            </MudItem>
        </MudGrid>
    </MudPaper>
</div>
    </Authorized>
    <NotAuthorized>
        <div>
            <MudPaper Class="pa-6">
                <MudStack AlignItems="AlignItems.Center" Spacing="4">
                    <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Large" Color="Color.Warning" />
                    <MudText Typo="Typo.h5">需要登录</MudText>
                    <MudText Typo="Typo.body1" Color="Color.Secondary">请先登录后再访问此页面</MudText>
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="/login" StartIcon="@Icons.Material.Filled.Login">
                        前往登录
                    </MudButton>
                </MudStack>
            </MudPaper>
        </div>
    </NotAuthorized>
</AuthorizeView>

@code {
    private List<RepairOrderDetailDto> repairOrders = new();
    private List<RepairOrderDetailDto> filteredRepairOrders = new();
    private List<Department> departments = new();
    private RepairOrderStatisticsDto statistics = new();
    private RepairOrderSearchDto searchDto = new();
    private bool loading = false;
    private int currentUserId = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentUser();
        await LoadDepartments();
        await LoadRepairOrders();
        await LoadStatistics();
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var userIdClaim = authState.User.FindFirst("UserId") ?? authState.User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
                {
                    currentUserId = userId;
                }
                else
                {
                    // 如果无法获取用户ID，使用默认值1（这种情况应该很少发生）
                    currentUserId = 1;
                    Snackbar.Add("无法获取用户ID，使用默认值", Severity.Warning);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"获取当前用户信息失败: {ex.Message}", Severity.Error);
            // 使用默认值以防止页面崩溃
            currentUserId = 1;
        }
    }

    private async Task LoadDepartments()
    {
        try
        {
            departments = await DepartmentService.GetEnabledDepartmentsAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载部门数据失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadRepairOrders()
    {
        loading = true;
        try
        {
            repairOrders = await RepairOrderService.GetRepairOrderDetailsAsync();
            filteredRepairOrders = repairOrders.ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载报修单数据失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private async Task LoadStatistics()
    {
        try
        {
            statistics = await RepairOrderService.GetRepairOrderStatisticsAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载统计数据失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task SearchRepairOrders()
    {
        loading = true;
        try
        {
            filteredRepairOrders = await RepairOrderService.SearchRepairOrdersAsync(searchDto);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"搜索报修单失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private async Task ClearSearch()
    {
        searchDto = new RepairOrderSearchDto();
        await LoadRepairOrders();
    }

    private async Task ShowMyRepairOrders()
    {
        searchDto = new RepairOrderSearchDto { ReporterId = currentUserId };
        await SearchRepairOrders();
    }

    private Color GetUrgencyColor(int urgencyLevel)
    {
        return urgencyLevel switch
        {
            1 => Color.Error,    // 紧急
            2 => Color.Warning,  // 高
            3 => Color.Info,     // 中
            4 => Color.Default,  // 低
            _ => Color.Default
        };
    }

    private Color GetStatusColor(int status)
    {
        return CoreHub.Shared.Utils.RepairOrderStatusHelper.GetStatusColor(status);
    }

    private bool CanEditRepairOrder(RepairOrderDetailDto repairOrder)
    {
        // 只有报修人可以编辑待处理状态的报修单
        return repairOrder.ReporterId == currentUserId && repairOrder.Status == CoreHub.Shared.Utils.RepairOrderStatusHelper.Pending;
    }

    private bool CanCancelRepairOrder(RepairOrderDetailDto repairOrder)
    {
        // 报修人可以作废待处理和处理中的报修单
        return repairOrder.ReporterId == currentUserId &&
               (repairOrder.Status == CoreHub.Shared.Utils.RepairOrderStatusHelper.Pending ||
                repairOrder.Status == CoreHub.Shared.Utils.RepairOrderStatusHelper.InProgress);
    }

    private bool CanDeleteRepairOrder(RepairOrderDetailDto repairOrder)
    {
        // 只有报修人可以删除待处理状态的报修单
        return repairOrder.ReporterId == currentUserId && repairOrder.Status == CoreHub.Shared.Utils.RepairOrderStatusHelper.Pending;
    }

    private bool CanConfirmRepair(RepairOrderDetailDto repairOrder)
    {
        // 只有报修人可以确认待确认状态的报修单
        return repairOrder.ReporterId == currentUserId && repairOrder.Status == CoreHub.Shared.Utils.RepairOrderStatusHelper.PendingConfirmation;
    }

    private void CreateRepairOrder()
    {
        Navigation.NavigateTo("/create-repair-order");
    }

    private async Task ViewRepairOrderDetail(RepairOrderDetailDto repairOrder)
    {
        var parameters = new DialogParameters<RepairOrderDetailDialog>
        {
            { x => x.RepairOrderDetail, repairOrder }
        };

        await DialogService.ShowAsync<RepairOrderDetailDialog>("报修单详情", parameters);
    }

    private void EditRepairOrder(RepairOrderDetailDto repairOrder)
    {
        Navigation.NavigateTo($"/edit-repair-order/{repairOrder.Id}");
    }

    private async Task CancelRepairOrder(RepairOrderDetailDto repairOrder)
    {
        var parameters = new DialogParameters<CancelRepairOrderDialog>
        {
            { x => x.RepairOrderId, repairOrder.Id },
            { x => x.OrderNumber, repairOrder.OrderNumber }
        };

        var dialog = await DialogService.ShowAsync<CancelRepairOrderDialog>("作废报修单", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadRepairOrders();
            await LoadStatistics();
        }
    }

    private async Task DeleteRepairOrder(RepairOrderDetailDto repairOrder)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除报修单 '{repairOrder.OrderNumber}' 吗？此操作不可撤销。",
            yesText: "删除",
            cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                var result = await RepairOrderService.DeleteRepairOrderAsync(repairOrder.Id);
                if (result.IsSuccess)
                {
                    Snackbar.Add("报修单删除成功", Severity.Success);
                    await LoadRepairOrders();
                    await LoadStatistics();
                }
                else
                {
                    Snackbar.Add($"删除失败: {result.ErrorMessage}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task ExportRepairOrders()
    {
        // TODO: 实现导出功能
        Snackbar.Add("导出功能开发中...", Severity.Info);
    }

    private async Task ConfirmRepair(RepairOrderDetailDto repairOrder)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "确认维修完成",
            $"您确认报修单 '{repairOrder.OrderNumber}' 的维修工作已经完成吗？",
            yesText: "确认完成",
            cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                // 更新状态为已完成
                var result = await RepairOrderService.UpdateRepairOrderStatusAsync(repairOrder.Id, CoreHub.Shared.Utils.RepairOrderStatusHelper.Completed);
                if (result.IsSuccess)
                {
                    // 记录工作流历史
                    await RepairWorkflowService.AddWorkflowHistoryAsync(
                        repairOrder.Id,
                        currentUserId,
                        "确认维修完成",
                        "报修人确认维修工作已完成",
                        CoreHub.Shared.Utils.RepairOrderStatusHelper.PendingConfirmation, // 从待确认
                        CoreHub.Shared.Utils.RepairOrderStatusHelper.Completed  // 到已完成
                    );

                    Snackbar.Add("确认完成成功", Severity.Success);
                    await LoadRepairOrders();
                    await LoadStatistics();
                }
                else
                {
                    Snackbar.Add($"确认失败: {result.ErrorMessage}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"确认失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task RequestRework(RepairOrderDetailDto repairOrder)
    {
        var parameters = new DialogParameters<RequestReworkDialog>
        {
            { x => x.RepairOrderId, repairOrder.Id },
            { x => x.OrderNumber, repairOrder.OrderNumber }
        };

        var dialog = await DialogService.ShowAsync<RequestReworkDialog>("要求重新维修", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadRepairOrders();
            await LoadStatistics();
        }
    }
}
