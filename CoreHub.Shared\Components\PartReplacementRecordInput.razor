@using CoreHub.Shared.Models.Dto
@using FluentValidation
@inject ISnackbar Snackbar
@inject CoreHub.Shared.Services.OutsourcedProcessingService OutsourcedProcessingService

<MudCard Class="mb-4">
    <MudCardHeader>
        <CardHeaderContent>
            <MudText Typo="Typo.h6">
                <MudIcon Icon="@Icons.Material.Filled.Build" Class="mr-2" />
                零件更换记录
                @if (PartRecords.HasAnyParts)



                {
                    <MudChip T="string" Color="Color.Info" Size="Size.Small" Class="ml-2">
                        @PartRecords.TotalCount 项
                    </MudChip>
                }
            </MudText>
        </CardHeaderContent>
        <CardHeaderActions>
            <MudButton Variant="Variant.Outlined" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add"
                OnClick="OnAddPartButtonClick" Size="Size.Small">
                添加零件
            </MudButton>
        </CardHeaderActions>
    </MudCardHeader>
    <MudCardContent>
        @if (!PartRecords.HasAnyParts)



        {
            <MudAlert Severity="Severity.Info" Class="mb-4">
                <MudText>暂无零件更换记录。如需更换零件，请点击"添加零件"按钮。</MudText>
            </MudAlert>
        }



        else



        {
            <MudTable Items="@PartRecords.PartRequests" Dense="true" Hover="true" Breakpoint="Breakpoint.Sm" Class="mb-4">
                <HeaderContent>
                    <MudTh>零件名称</MudTh>
                    <MudTh>规格型号</MudTh>
                    <MudTh>数量</MudTh>
                    <MudTh>状态</MudTh>
                    <MudTh>更换原因</MudTh>
                    <MudTh Style="width: 120px;">操作</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="零件名称">
                        <MudText Typo="Typo.body2">@context.PartName</MudText>
                    </MudTd>
                    <MudTd DataLabel="规格型号">
                        <MudText Typo="Typo.body2" Color="Color.Secondary">
                            @(string.IsNullOrWhiteSpace(context.Specification) ? "-" : context.Specification)
                        </MudText>
                    </MudTd>
                    <MudTd DataLabel="数量">
                        <MudText Typo="Typo.body2">@context.QuantityDescription</MudText>
                    </MudTd>
                    <MudTd DataLabel="状态">
                        <MudChip T="string" Color="@GetStatusColor(context.Status)" Size="Size.Small">
                            @context.StatusName
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="更换原因">
                        <MudText Typo="Typo.body2" Color="Color.Secondary">
                            @(string.IsNullOrWhiteSpace(context.Reason) ? "-" :
                            (context.Reason.Length > 20 ? context.Reason.Substring(0, 20) + "..." : context.Reason))
                        </MudText>
                    </MudTd>
                    <MudTd DataLabel="操作">
                        <MudStack Row Spacing="1">
                             <!-- 编辑按钮 -->
                             @if (CoreHub.Shared.Utils.PartRequestStatusHelper.CanEdit(context.Status))



                            {
                                <MudIconButton Icon="@Icons.Material.Filled.Edit" Size="Size.Small" Color="Color.Primary"
                                    OnClick="() => ShowEditPartDialog(context)" Title="编辑" />
                            }

                            <!-- 无需申请按钮 -->
                            @if (CoreHub.Shared.Utils.PartRequestStatusHelper.CanMarkAsNotRequired(context.Status))



                            {
                                <MudIconButton Icon="@Icons.Material.Filled.Inventory" Size="Size.Small" Color="Color.Info"
                                    OnClick="() => ShowMarkAsNotRequiredConfirm(context.TempId, context.PartName)"
                                    Title="已有零件，无需申请" />
                            }

                            <!-- 委外加工按钮 -->
                            @if (CoreHub.Shared.Utils.PartRequestStatusHelper.CanOutsourceProcessing(context.Status) && context.Id > 0)

                            {
                                <MudIconButton Icon="@Icons.Material.Filled.Engineering" Size="Size.Small"
                                    Color="Color.Secondary"
                                    OnClick="() => ShowOutsourceProcessingDialog(context.TempId, context.PartName)"
                                    Title="委外加工" />
                            }

                            <!-- 委外加工进度按钮 -->
                            @if (CoreHub.Shared.Utils.PartRequestStatusHelper.CanManageOutsourcedProgress(context.Status) && context.Id > 0)

                            {
                                <MudIconButton Icon="@Icons.Material.Filled.Timeline" Size="Size.Small" Color="Color.Secondary"
                                    OnClick="async () => await ShowOutsourcedProgressDialog(context.TempId, context.PartName)"
                                    Title="查看委外加工进度" />
                            }

                            <!-- 直接完成按钮 -->
                            @if (CoreHub.Shared.Utils.PartRequestStatusHelper.CanDirectComplete(context.Status))



                            {
                                <MudIconButton Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Small" Color="Color.Success"
                                    OnClick="() => ShowDirectCompleteConfirm(context.TempId, context.PartName)"
                                    Title="直接标记为已安装" />
                            }

                            <!-- 标记为已安装按钮 -->
                            @if (CoreHub.Shared.Utils.PartRequestStatusHelper.CanInstall(context.Status))



                            {
                                <MudIconButton Icon="@Icons.Material.Filled.Build" Size="Size.Small" Color="Color.Success"
                                    OnClick="() => ShowMarkAsInstalledConfirm(context.TempId, context.PartName)"
                                    Title="标记为已安装" />
                            }

                            <!-- 委外加工记录查看按钮 -->
                            @if ((CoreHub.Shared.Utils.PartRequestStatusHelper.CanManageOutsourcedProgress(context.Status) || 
                                  CoreHub.Shared.Utils.PartRequestStatusHelper.CanViewOutsourcedRecord(context.Status)) && 
                                 context.Id > 0)
                            {
                                <MudIconButton Icon="@Icons.Material.Filled.History" Size="Size.Small" Color="Color.Info"
                                    OnClick="async () => await ShowOutsourcedProgressDialog(context.TempId, context.PartName)"
                                    Title="@(CoreHub.Shared.Utils.PartRequestStatusHelper.CanManageOutsourcedProgress(context.Status) ? "管理委外加工进度" : "查看委外加工记录")" />
                            }

                            <!-- 删除按钮 -->
                            @if (CoreHub.Shared.Utils.PartRequestStatusHelper.CanDelete(context.Status))



                            {
                                <MudIconButton Icon="@Icons.Material.Filled.Delete" Size="Size.Small" Color="Color.Error"
                                    OnClick="() => ShowDeleteConfirm(context.TempId, context.PartName)" Title="删除记录" />
                            }
                        </MudStack>
                    </MudTd>
                </RowTemplate>
            </MudTable>

            <!-- 统计信息 -->
            <MudGrid>
                <MudItem xs="12" sm="6" md="3">
                    <MudPaper Class="pa-4 text-center">
                        <MudText Typo="Typo.h6" Color="Color.Primary">@PartRecords.TotalCount</MudText>
                        <MudText Typo="Typo.caption">总记录数</MudText>
                    </MudPaper>
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <MudPaper Class="pa-4 text-center">
                        <MudText Typo="Typo.h6" Color="Color.Warning">@PartRecords.PendingCount</MudText>
                        <MudText Typo="Typo.caption">申请中</MudText>
                    </MudPaper>
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <MudPaper Class="pa-4 text-center">
                        <MudText Typo="Typo.h6" Color="Color.Success">@PartRecords.CompletedCount</MudText>
                        <MudText Typo="Typo.caption">已完成</MudText>
                    </MudPaper>
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <MudPaper Class="pa-4 text-center">
                        <MudText Typo="Typo.h6" Color="Color.Info">@PartRecords.TotalRequestedQuantity</MudText>
                        <MudText Typo="Typo.caption">总数量</MudText>
                    </MudPaper>
                </MudItem>
            </MudGrid>
        }
    </MudCardContent>
</MudCard>

<!-- 添加/编辑零件对话框 -->
<MudDialog @bind-Visible="showPartDialog" Options="dialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Build" Class="mr-2" />
            @(isEditMode ? "编辑零件记录" : "添加零件记录")
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudForm @ref="partForm" Model="@currentPart" Validation="@(new PartReplacementRequestValidator())">
            <MudGrid>
                <MudItem xs="12" md="8">
                    <MudTextField @bind-Value="currentPart.PartName" For="@(() => currentPart.PartName)" Label="零件名称"
                        Required="true" Immediate="true" HelperText="请输入零件的具体名称" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="currentPart.Unit" For="@(() => currentPart.Unit)" Label="计量单位"
                        Required="true" Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="currentPart.Specification" For="@(() => currentPart.Specification)"
                        Label="规格型号" Immediate="true" HelperText="请输入零件的规格、型号等详细信息" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudNumericField @bind-Value="currentPart.RequestedQuantity"
                        For="@(() => currentPart.RequestedQuantity)" Label="申请数量" Required="true" Min="1" Max="9999"
                        Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="currentPart.Reason" For="@(() => currentPart.Reason)" Label="更换原因"
                        Lines="2" Immediate="true" HelperText="请说明为什么需要更换此零件" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="currentPart.Remark" For="@(() => currentPart.Remark)" Label="备注"
                        Lines="2" Immediate="true" HelperText="其他需要说明的信息" />
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CancelPartDialog">取消</MudButton>
        <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="SavePart"
            StartIcon="@Icons.Material.Filled.Save">
            @(isEditMode ? "保存" : "添加")
        </MudButton>
    </DialogActions>
</MudDialog>

<!-- 确认操作对话框 -->
<MudDialog @bind-Visible="showConfirmDialog" Options="confirmDialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Warning" Class="mr-2" />
            @confirmTitle
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudText Typo="Typo.body1">@confirmMessage</MudText>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CancelConfirm">取消</MudButton>
        <MudButton Color="@confirmColor" Variant="Variant.Filled" OnClick="ExecuteConfirmedAction">
            @confirmButtonText
        </MudButton>
    </DialogActions>
</MudDialog>

<!-- 委外加工对话框 -->
<MudDialog @bind-Visible="showOutsourceDialog" Options="dialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Engineering" Class="mr-2" />
            委外加工 - @currentOutsourcePartName
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudForm @ref="outsourceForm" Model="@currentOutsourceRecord">
            <MudGrid>
                <MudItem xs="12" md="8">
                    <MudTextField @bind-Value="currentOutsourceRecord.SupplierName"
                        For="@(() => currentOutsourceRecord.SupplierName)" Label="供应商名称" Required="true"
                        Immediate="true" HelperText="请输入委外加工的供应商名称" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="currentOutsourceRecord.SupplierContact"
                        For="@(() => currentOutsourceRecord.SupplierContact)" Label="联系人" Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="currentOutsourceRecord.SupplierPhone"
                        For="@(() => currentOutsourceRecord.SupplierPhone)" Label="联系电话" Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudDatePicker @bind-Date="currentOutsourceRecord.EstimatedCompletionDate" Label="预计完成时间"
                        HelperText="预计的加工完成时间" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="currentOutsourceRecord.ProcessingRequirements"
                        For="@(() => currentOutsourceRecord.ProcessingRequirements)" Label="加工要求" Lines="3"
                        Immediate="true" HelperText="请详细描述加工要求和技术规范" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudNumericField @bind-Value="currentOutsourceRecord.ProcessingCost"
                        For="@(() => currentOutsourceRecord.ProcessingCost)" Label="预计加工费用" Min="0" Format="F2"
                        Immediate="true" HelperText="预计的加工费用（元）" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="currentOutsourceRecord.Remark"
                        For="@(() => currentOutsourceRecord.Remark)" Label="备注" Lines="2" Immediate="true"
                        HelperText="其他需要说明的信息" />
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CancelOutsourceDialog">取消</MudButton>
        <MudButton Color="Color.Secondary" Variant="Variant.Filled" OnClick="SaveOutsourceRecord"
            StartIcon="@Icons.Material.Filled.Engineering">
            创建委外加工
        </MudButton>
    </DialogActions>
</MudDialog>

<!-- 委外加工进度对话框 -->
<MudDialog @bind-Visible="showProgressDialog" Options="progressDialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Engineering" Class="mr-2" />
            委外加工状态 - @currentProgressPartName
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudGrid>
            <MudItem xs="12">
                <MudCard Elevation="2" Class="pa-4">
                    <MudCardContent>
                        <MudStack Spacing="3">
                            <MudText Typo="Typo.h6" Color="Color.Primary">委外加工信息</MudText>
                            
                            @if (currentOutsourceRecord != null)
                            {
                                <MudGrid>
                                    <MudItem xs="12" md="6">
                                        <MudTextField Label="供应商名称" 
                                                      Value="@currentOutsourceRecord.SupplierName" 
                                                      ReadOnly="true" />
                                    </MudItem>
                                    <MudItem xs="12" md="6">
                                        <MudTextField Label="联系人" 
                                                      Value="@currentOutsourceRecord.SupplierContact" 
                                                      ReadOnly="true" />
                                    </MudItem>
                                    <MudItem xs="12" md="6">
                                        <MudTextField Label="加工状态" 
                                                      Value="@GetProcessingStatusName(currentOutsourceRecord.ProcessingStatus)" 
                                                      ReadOnly="true" />
                                    </MudItem>
                                    <MudItem xs="12" md="6">
                                        <MudDatePicker Label="预计完成时间" 
                                                       Date="@currentOutsourceRecord.EstimatedCompletionDate" 
                                                       ReadOnly="true" />
                                    </MudItem>
                                    @if (currentOutsourceRecord.ProcessingStatus >= 4) // 已完成状态
                                    {
                                        <MudItem xs="12" md="6">
                                            <MudTextField Label="实际完成时间"
                                                          Value="@(currentOutsourceRecord.ActualCompletionDate?.ToString("yyyy-MM-dd HH:mm:ss") ?? "未设置")"
                                                          ReadOnly="true" />
                                        </MudItem>
                                    }
                                </MudGrid>
                            }
                            else
                            {
                                <MudAlert Severity="Severity.Warning">
                                    委外加工记录加载失败，请重试。
                                </MudAlert>
                            }
                            
                            @if (canManageProgress && currentOutsourceRecord != null && currentOutsourceRecord.ProcessingStatus < 4)
                            {
                                <MudDivider />
                                <MudText Typo="Typo.h6" Color="Color.Secondary">更新状态</MudText>
                                
                                <MudGrid>
                                    <MudItem xs="12" md="6">
                                        <MudSelect @bind-Value="newProcessingStatus" 
                                                   Label="加工状态" 
                                                   Required="true">
                                            <MudSelectItem Value="2">加工中</MudSelectItem>
                                            <MudSelectItem Value="3">待验收</MudSelectItem>
                                            <MudSelectItem Value="4">已完成</MudSelectItem>
                                            <MudSelectItem Value="5">已取消</MudSelectItem>
                                        </MudSelect>
                                    </MudItem>
                                    @if (newProcessingStatus == 4)
                                    {
                                        <MudItem xs="12" md="6">
                                            <MudAlert Severity="Severity.Info" Class="mb-3">
                                                <MudText Typo="Typo.body2">
                                                    <strong>提示：</strong>选择"已完成"状态时，系统将自动记录当前时间作为实际完成时间。
                                                </MudText>
                                            </MudAlert>
                                        </MudItem>
                                    }
                                    <MudItem xs="12">
                                        <MudTextField @bind-Value="statusUpdateRemark" 
                                                      Label="备注" 
                                                      Lines="2" 
                                                      Placeholder="请输入状态更新说明..." />
                                    </MudItem>
                                </MudGrid>
                            }
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseProgressDialog">关闭</MudButton>
        @if (canManageProgress && currentOutsourceRecord != null && currentOutsourceRecord.ProcessingStatus < 4)
        {
            <MudButton Color="Color.Primary" 
                       Variant="Variant.Filled" 
                       OnClick="UpdateProcessingStatus"
                       StartIcon="@Icons.Material.Filled.Update">
                更新状态
            </MudButton>
        }
    </DialogActions>
</MudDialog>

@code {
    [Parameter] public PartReplacementRequestCollectionDto PartRecords { get; set; } = new();



    [Parameter] public EventCallback<PartReplacementRequestCollectionDto> PartRecordsChanged { get; set; }



    [Parameter] public bool ReadOnly { get; set; } = false;



    [Parameter] public int RepairOrderId { get; set; }



    [Parameter] public int CurrentUserId { get; set; }







    private MudForm partForm = null!;



    private bool showPartDialog = false;



    private bool isEditMode = false;



    private PartReplacementRequestDto currentPart = new();



    private string editingTempId = string.Empty;







    // 确认对话框相关变量



    private bool showConfirmDialog = false;



    private string confirmTitle = string.Empty;



    private string confirmMessage = string.Empty;



    private Color confirmColor = Color.Primary;



    private string confirmButtonText = string.Empty;



    private Func<Task>? confirmAction = null;



    // 委外加工相关变量

    private MudForm outsourceForm = null!;

    private bool showOutsourceDialog = false;

    private OutsourcedProcessingDto currentOutsourceRecord = new();

    private string currentOutsourcePartName = string.Empty;

    private string currentOutsourceTempId = string.Empty;



    // 委外加工进度相关变量

    private bool showProgressDialog = false;

    private string currentProgressPartName = string.Empty;



    private bool canManageProgress = false;



    // 状态更新相关变量

    private int newProcessingStatus = 1;

    private string statusUpdateRemark = string.Empty;







    private DialogOptions dialogOptions = new()



        {



            MaxWidth = MaxWidth.Medium,



            FullWidth = true,



            CloseButton = true,



            BackdropClick = false



        };



    private DialogOptions progressDialogOptions = new()

        {

            MaxWidth = MaxWidth.Large,

            FullWidth = true,

            CloseButton = true,

            BackdropClick = false

        };







    private DialogOptions confirmDialogOptions = new()



        {



            MaxWidth = MaxWidth.Small,



            FullWidth = true,



            CloseButton = true,



            BackdropClick = false



        };







    private Color GetStatusColor(int status) => CoreHub.Shared.Utils.PartRequestStatusHelper.GetStatusColor(status);







    private void OnAddPartButtonClick()



    {



        ShowAddPartDialog();



    }







    private void ShowAddPartDialog()



    {



        if (ReadOnly) return;







        isEditMode = false;



        currentPart = new PartReplacementRequestDto



            {



                RepairOrderId = RepairOrderId,



                RequestedBy = CurrentUserId



            };



        showPartDialog = true;



    }







    private void ShowEditPartDialog(PartReplacementRequestDto part)



    {



        if (ReadOnly || !part.CanEdit) return;







        isEditMode = true;



        editingTempId = part.TempId;



        currentPart = part.Clone();



        showPartDialog = true;



    }







    private void CancelPartDialog()



    {



        showPartDialog = false;



        currentPart = new PartReplacementRequestDto



            {



                RepairOrderId = RepairOrderId,



                RequestedBy = CurrentUserId



            };



        editingTempId = string.Empty;



    }







    private async Task SavePart()



    {



        await partForm.Validate();



        if (!partForm.IsValid) return;







        if (isEditMode)



        {



            // 更新现有记录



            currentPart.TempId = editingTempId;



            PartRecords.UpdatePart(currentPart);



        }



        else



        {



            // 添加新记录



            PartRecords.AddPart(currentPart);



        }







        await PartRecordsChanged.InvokeAsync(PartRecords);



        CancelPartDialog();



    }







    // 显示确认对话框的方法



    private void ShowMarkAsNotRequiredConfirm(string tempId, string partName)



    {



        if (ReadOnly) return;







        confirmTitle = "确认标记为无需申请";



        confirmMessage = $"确定要将零件「{partName}」标记为无需申请吗？此操作表示维修人员已有此零件，无需从仓库申请。";



        confirmColor = Color.Info;



        confirmButtonText = "确认标记";



        confirmAction = () => MarkAsNotRequired(tempId);



        showConfirmDialog = true;



    }







    private void ShowDirectCompleteConfirm(string tempId, string partName)



    {



        if (ReadOnly) return;







        confirmTitle = "确认直接完成";



        confirmMessage = $"确定要将零件「{partName}」直接标记为已安装吗？此操作将跳过申请和领用流程，直接完成零件更换。";



        confirmColor = Color.Success;



        confirmButtonText = "确认完成";



        confirmAction = () => DirectComplete(tempId);



        showConfirmDialog = true;



    }







    private void ShowMarkAsInstalledConfirm(string tempId, string partName)



    {



        if (ReadOnly) return;







        confirmTitle = "确认标记为已安装";



        confirmMessage = $"确定要将零件「{partName}」标记为已安装吗？此操作表示零件已成功安装完成。";



        confirmColor = Color.Success;



        confirmButtonText = "确认安装";



        confirmAction = () => MarkAsInstalled(tempId);



        showConfirmDialog = true;



    }







    private void ShowDeleteConfirm(string tempId, string partName)



    {



        if (ReadOnly) return;







        confirmTitle = "确认删除记录";



        confirmMessage = $"确定要删除零件「{partName}」的记录吗？此操作不可撤销，记录将被永久删除。";



        confirmColor = Color.Error;



        confirmButtonText = "确认删除";



        confirmAction = () => DeletePart(tempId);



        showConfirmDialog = true;



    }







    // 取消确认操作



    private void CancelConfirm()



    {



        showConfirmDialog = false;



        confirmAction = null;



    }







    // 执行确认的操作



    private async Task ExecuteConfirmedAction()



    {



        if (confirmAction != null)



        {



            await confirmAction();



        }



        showConfirmDialog = false;



        confirmAction = null;



    }







    // 实际执行操作的方法



    private async Task MarkAsNotRequired(string tempId)



    {



        if (ReadOnly) return;







        var part = PartRecords.GetPart(tempId);



        if (part != null && CoreHub.Shared.Utils.PartRequestStatusHelper.CanMarkAsNotRequired(part.Status))



        {



            part.Status = CoreHub.Shared.Utils.PartRequestStatusHelper.NotRequired;



            part.UpdatedAt = DateTime.Now;



            await PartRecordsChanged.InvokeAsync(PartRecords);



        }



    }







    private async Task DirectComplete(string tempId)



    {



        if (ReadOnly) return;







        var part = PartRecords.GetPart(tempId);



        if (part != null && CoreHub.Shared.Utils.PartRequestStatusHelper.CanDirectComplete(part.Status))



        {



            part.Status = CoreHub.Shared.Utils.PartRequestStatusHelper.Installed;



            part.UpdatedAt = DateTime.Now;



            await PartRecordsChanged.InvokeAsync(PartRecords);



        }



    }







    private async Task MarkAsInstalled(string tempId)



    {



        if (ReadOnly) return;







        var part = PartRecords.GetPart(tempId);



        if (part != null && CoreHub.Shared.Utils.PartRequestStatusHelper.CanInstall(part.Status))



        {



            part.Status = CoreHub.Shared.Utils.PartRequestStatusHelper.Installed;



            part.UpdatedAt = DateTime.Now;



            await PartRecordsChanged.InvokeAsync(PartRecords);



        }



    }







    private async Task DeletePart(string tempId)



    {



        if (ReadOnly) return;







        var part = PartRecords.GetPart(tempId);



        if (part != null && CoreHub.Shared.Utils.PartRequestStatusHelper.CanDelete(part.Status))



        {



            PartRecords.RemovePart(tempId);



            await PartRecordsChanged.InvokeAsync(PartRecords);



        }



    }



    #region 委外加工相关方法


    /// <summary>
    /// 显示委外加工对话框
    /// </summary>
    /// <param name="tempId">零件临时ID</param>
    /// <param name="partName">零件名称</param>
    private void ShowOutsourceProcessingDialog(string tempId, string partName)
    {
        if (ReadOnly) return;

        var part = PartRecords.GetPart(tempId);
        if (part == null)
        {
            Snackbar.Add("零件记录信息无效", Severity.Warning);
            return;
        }

        if (part.Id <= 0)
        {
            Snackbar.Add("请先保存零件记录后再创建委外加工", Severity.Warning);
            return;
        }

        currentOutsourceTempId = tempId;
        currentOutsourcePartName = partName;
        currentOutsourceRecord = new OutsourcedProcessingDto();
        showOutsourceDialog = true;
    }



    /// <summary>
    /// 取消委外加工对话框
    /// </summary>
    private void CancelOutsourceDialog()

    {

        showOutsourceDialog = false;

        currentOutsourceRecord = new OutsourcedProcessingDto();

        currentOutsourceTempId = string.Empty;

        currentOutsourcePartName = string.Empty;

    }



    /// <summary>
    /// 保存委外加工记录
    /// </summary>
    private async Task SaveOutsourceRecord()
    {
        await outsourceForm.Validate();
        if (!outsourceForm.IsValid) return;

        var part = PartRecords.GetPart(currentOutsourceTempId);
        if (part == null)
        {
            Snackbar.Add("零件记录信息无效", Severity.Error);
            return;
        }

        if (!CoreHub.Shared.Utils.PartRequestStatusHelper.CanOutsourceProcessing(part.Status))
        {
            Snackbar.Add("当前零件状态不允许委外加工", Severity.Warning);
            return;
        }

        // 只有已保存到数据库的零件记录才能创建委外加工记录
        if (part.Id <= 0)
        {
            Snackbar.Add("请先保存零件记录后再创建委外加工", Severity.Warning);
            return;
        }

        try
        {
            // 调用服务创建委外加工记录
            var result = await OutsourcedProcessingService.CreateOutsourcedProcessingAsync(
                part.Id, 
                currentOutsourceRecord, 
                CurrentUserId);

            if (result.Success)
            {
                // 更新零件状态为委外加工
                part.Status = CoreHub.Shared.Utils.PartRequestStatusHelper.OutsourcedProcessing;
                part.UpdatedAt = DateTime.Now;

                await PartRecordsChanged.InvokeAsync(PartRecords);
                
                Snackbar.Add("委外加工记录创建成功", Severity.Success);
                CancelOutsourceDialog();
            }
            else
            {
                Snackbar.Add($"创建委外加工记录失败: {result.Message}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"创建委外加工记录失败: {ex.Message}", Severity.Error);
        }
    }



    /// <summary>
    /// 显示委外加工进度对话框
    /// </summary>
    /// <param name="tempId">零件临时ID</param>
    /// <param name="partName">零件名称</param>
    private async Task ShowOutsourcedProgressDialog(string tempId, string partName)
    {
        if (ReadOnly) return;

        currentProgressPartName = partName;
        
        // 获取当前零件的委外加工记录
        var part = PartRecords.GetPart(tempId);
        if (part == null)
        {
            Snackbar.Add("零件记录信息无效", Severity.Warning);
            return;
        }

        // 只有已保存到数据库的零件记录才能查看委外加工进度
        if (part.Id <= 0)
        {
            Snackbar.Add("请先保存零件记录后再查看委外加工进度", Severity.Warning);
            return;
        }

        try
        {
            // 从数据库获取委外加工记录
            currentOutsourceRecord = await OutsourcedProcessingService.GetOutsourcedProcessingByPartRequestAsync(part.Id);
            
            if (currentOutsourceRecord == null)
            {
                Snackbar.Add("该零件没有委外加工记录", Severity.Info);
                return;
            }

            // 初始化状态更新变量
            newProcessingStatus = currentOutsourceRecord.ProcessingStatus;
            statusUpdateRemark = string.Empty;
            
            // 根据零件状态决定是否可以管理进度
            // 委外加工状态可以管理进度，已安装状态只能查看历史记录
            canManageProgress = CoreHub.Shared.Utils.PartRequestStatusHelper.CanManageOutsourcedProgress(part.Status);
            showProgressDialog = true;
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载委外加工记录失败: {ex.Message}", Severity.Error);
        }
    }



    /// <summary>
    /// 关闭委外加工进度对话框
    /// </summary>
    private void CloseProgressDialog()
    {
        showProgressDialog = false;
        currentProgressPartName = string.Empty;
        canManageProgress = false;
        
        // 清理状态更新变量
        newProcessingStatus = 1;
        statusUpdateRemark = string.Empty;
    }



    /// <summary>
    /// 更新委外加工状态
    /// </summary>
    private async Task UpdateProcessingStatus()
    {
        try
        {

            // 调用服务更新数据库
            if (currentOutsourceRecord?.Id > 0)
            {
                var result = await OutsourcedProcessingService.UpdateProcessingStatusAsync(
                    currentOutsourceRecord.Id,
                    newProcessingStatus,
                    statusUpdateRemark,
                    CurrentUserId);

                if (result.Success)
                {
                    // 更新本地对象状态
                    currentOutsourceRecord.ProcessingStatus = newProcessingStatus;
                    if (newProcessingStatus == 4) // 已完成
                    {
                        // 设置为当前时间，与服务端保持一致
                        currentOutsourceRecord.ActualCompletionDate = DateTime.Now;
                    }

                    Snackbar.Add("委外加工状态更新成功", Severity.Success);
                    CloseProgressDialog();
                }
                else
                {
                    Snackbar.Add($"更新失败: {result.Message}", Severity.Error);
                }
            }
            else
            {
                Snackbar.Add("委外加工记录信息无效，无法更新状态", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"更新状态失败: {ex.Message}", Severity.Error);
        }
    }



    /// <summary>
    /// 获取委外加工状态名称
    /// </summary>
    private string GetProcessingStatusName(int status)
    {
        return status switch
        {
            1 => "已下单",
            2 => "加工中",
            3 => "待验收",
            4 => "已完成",
            5 => "已取消",
            _ => "未知"
        };
    }



    #endregion






    public class PartReplacementRequestValidator : AbstractValidator<PartReplacementRequestDto>



    {



        public PartReplacementRequestValidator()



        {



            RuleFor(x => x.PartName)



            .NotEmpty().WithMessage("零件名称不能为空")



            .MaximumLength(100).WithMessage("零件名称长度不能超过100个字符");







            RuleFor(x => x.Specification)



            .MaximumLength(200).WithMessage("规格型号长度不能超过200个字符");







            RuleFor(x => x.RequestedQuantity)



            .GreaterThan(0).WithMessage("申请数量必须大于0")



            .LessThanOrEqualTo(9999).WithMessage("申请数量不能超过9999");







            RuleFor(x => x.Unit)



            .NotEmpty().WithMessage("计量单位不能为空")



            .MaximumLength(20).WithMessage("计量单位长度不能超过20个字符");







            RuleFor(x => x.Reason)



            .MaximumLength(500).WithMessage("更换原因长度不能超过500个字符");







            RuleFor(x => x.Remark)



            .MaximumLength(1000).WithMessage("备注长度不能超过1000个字符");



        }







        public Func<object, string, Task<IEnumerable<string>>> ValidateValue => async (model, propertyName) =>



        {



            var result = await

    ValidateAsync(ValidationContext<PartReplacementRequestDto>.CreateWithOptions((PartReplacementRequestDto)model, x =>

    x.IncludeProperties(propertyName)));



            if (result.IsValid)



                return Array.Empty<string>();



            return result.Errors.Select(e => e.ErrorMessage);



        };



    }



    public class OutsourcedProcessingValidator : AbstractValidator<OutsourcedProcessingDto>

    {

        public OutsourcedProcessingValidator()

        {

            RuleFor(x => x.SupplierName)

            .NotEmpty().WithMessage("供应商名称不能为空")

            .MaximumLength(200).WithMessage("供应商名称长度不能超过200个字符");



            RuleFor(x => x.SupplierContact)

            .MaximumLength(100).WithMessage("联系人长度不能超过100个字符");



            RuleFor(x => x.SupplierPhone)

            .MaximumLength(50).WithMessage("联系电话长度不能超过50个字符");



            RuleFor(x => x.ProcessingRequirements)

            .MaximumLength(1000).WithMessage("加工要求长度不能超过1000个字符");



            RuleFor(x => x.ProcessingCost)

            .GreaterThanOrEqualTo(0).WithMessage("加工费用不能为负数")

            .When(x => x.ProcessingCost.HasValue);



            RuleFor(x => x.Remark)

            .MaximumLength(1000).WithMessage("备注长度不能超过1000个字符");

        }



        public Func<object, string, Task<IEnumerable<string>>> ValidateValue => async (model, propertyName) =>

        {

            var result = await
    ValidateAsync(ValidationContext<OutsourcedProcessingDto>.CreateWithOptions((OutsourcedProcessingDto)model, x =>
    x.IncludeProperties(propertyName)));

            if (result.IsValid)

                return Array.Empty<string>();

            return result.Errors.Select(e => e.ErrorMessage);

        };

    }
}
